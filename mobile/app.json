{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "hakimali-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#0ea5e9"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.hakimali.mobile", "buildNumber": "1.0.0", "infoPlist": {"NSCameraUsageDescription": "This app needs access to camera to capture asset photos and documents.", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to select asset photos and documents.", "NSLocationWhenInUseUsageDescription": "This app needs location access to help identify asset locations.", "NSContactsUsageDescription": "This app needs access to contacts for emergency contact information.", "NSFaceIDUsageDescription": "This app uses Face ID for secure authentication."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#0ea5e9"}, "package": "com.hakimali.mobile", "versionCode": 1, "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.READ_CONTACTS", "android.permission.SEND_SMS", "android.permission.USE_FINGERPRINT", "android.permission.USE_BIOMETRIC", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-router", ["expo-camera", {"cameraPermission": "Allow <PERSON><PERSON><PERSON> to access your camera to capture asset photos and documents."}], ["expo-image-picker", {"photosPermission": "Allow <PERSON><PERSON><PERSON><PERSON> to access your photos to select asset images and documents."}], ["expo-location", {"locationForegroundPermission": "Allow HakiMali to use your location to help identify asset locations."}], ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#0ea5e9"}], ["expo-local-authentication", {"faceIDPermission": "Allow <PERSON><PERSON> to use Face ID for secure authentication."}]], "scheme": "hakim<PERSON>", "extra": {"router": {"origin": false}, "eas": {"projectId": "your-project-id-here"}}, "owner": "hakim<PERSON>"}}