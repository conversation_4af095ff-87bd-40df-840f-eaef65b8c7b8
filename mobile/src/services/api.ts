import axios, { AxiosInstance, AxiosResponse } from 'axios'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { ApiResponse, PaginatedResponse } from '@/types'

const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3333' 
  : 'https://api.hakimali.com'

class ApiService {
  private api: AxiosInstance

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      async (config) => {
        const token = await this.getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          await this.removeToken()
          // Navigate to login screen - this would be handled by the auth store
        }
        return Promise.reject(error)
      }
    )
  }

  private async getToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem('hakimali_token')
    } catch (error) {
      console.error('Error getting token:', error)
      return null
    }
  }

  private async setToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem('hakimali_token', token)
    } catch (error) {
      console.error('Error setting token:', error)
    }
  }

  private async removeToken(): Promise<void> {
    try {
      await AsyncStorage.removeItem('hakimali_token')
    } catch (error) {
      console.error('Error removing token:', error)
    }
  }

  // Authentication
  async login(email: string, password: string): Promise<ApiResponse<{ user: any; token: string }>> {
    const response = await this.api.post('/api/v1/auth/login', { email, password })
    if (response.data.success && response.data.data.token) {
      await this.setToken(response.data.data.token)
    }
    return response.data
  }

  async register(userData: any): Promise<ApiResponse<{ user: any; token: string }>> {
    const response = await this.api.post('/api/v1/auth/register', userData)
    if (response.data.success && response.data.data.token) {
      await this.setToken(response.data.data.token)
    }
    return response.data
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/api/v1/auth/logout')
    } finally {
      await this.removeToken()
    }
  }

  async forgotPassword(email: string): Promise<ApiResponse<any>> {
    const response = await this.api.post('/api/v1/auth/forgot-password', { email })
    return response.data
  }

  async resetPassword(token: string, password: string): Promise<ApiResponse<any>> {
    const response = await this.api.post('/api/v1/auth/reset-password', { token, password })
    return response.data
  }

  async verifyEmail(token: string): Promise<ApiResponse<any>> {
    const response = await this.api.post('/api/v1/auth/verify-email', { token })
    return response.data
  }

  async verifyPhone(phone: string, code: string): Promise<ApiResponse<any>> {
    const response = await this.api.post('/api/v1/auth/verify-phone', { phone, code })
    return response.data
  }

  async resendVerification(): Promise<ApiResponse<any>> {
    const response = await this.api.post('/api/v1/auth/resend-verification')
    return response.data
  }

  // User Profile
  async getCurrentUser(): Promise<ApiResponse<{ user: any }>> {
    const response = await this.api.get('/api/v1/users/me')
    return response.data
  }

  async updateProfile(profileData: any): Promise<ApiResponse<{ user: any }>> {
    const response = await this.api.put('/api/v1/users/me', profileData)
    return response.data
  }

  async uploadDocuments(documentType: string, fileUrl: string): Promise<ApiResponse<any>> {
    const response = await this.api.post('/api/v1/users/upload-documents', {
      documentType,
      fileUrl,
    })
    return response.data
  }

  async getKycStatus(): Promise<ApiResponse<any>> {
    const response = await this.api.get('/api/v1/users/kyc-status')
    return response.data
  }

  async submitKyc(): Promise<ApiResponse<any>> {
    const response = await this.api.post('/api/v1/users/submit-kyc')
    return response.data
  }

  // Assets
  async getAssets(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await this.api.get('/api/v1/assets', { params })
    return response.data
  }

  async getAsset(id: number): Promise<ApiResponse<{ asset: any }>> {
    const response = await this.api.get(`/api/v1/assets/${id}`)
    return response.data
  }

  async createAsset(assetData: any): Promise<ApiResponse<{ asset: any }>> {
    const response = await this.api.post('/api/v1/assets', assetData)
    return response.data
  }

  async updateAsset(id: number, assetData: any): Promise<ApiResponse<{ asset: any }>> {
    const response = await this.api.put(`/api/v1/assets/${id}`, assetData)
    return response.data
  }

  async deleteAsset(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete(`/api/v1/assets/${id}`)
    return response.data
  }

  async uploadAssetImages(id: number, imageUrls: string[]): Promise<ApiResponse<any>> {
    const response = await this.api.post(`/api/v1/assets/${id}/images`, { imageUrls })
    return response.data
  }

  async uploadAssetDocuments(id: number, documentUrls: string[]): Promise<ApiResponse<any>> {
    const response = await this.api.post(`/api/v1/assets/${id}/documents`, { documentUrls })
    return response.data
  }

  async getAssetValuations(id: number): Promise<ApiResponse<{ valuations: any[] }>> {
    const response = await this.api.get(`/api/v1/assets/${id}/valuations`)
    return response.data
  }

  async addAssetValuation(id: number, valuationData: any): Promise<ApiResponse<any>> {
    const response = await this.api.post(`/api/v1/assets/${id}/valuations`, valuationData)
    return response.data
  }

  // Loans
  async getLoans(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await this.api.get('/api/v1/loans', { params })
    return response.data
  }

  async getLoan(id: number): Promise<ApiResponse<{ loan: any }>> {
    const response = await this.api.get(`/api/v1/loans/${id}`)
    return response.data
  }

  async createLoanApplication(loanData: any): Promise<ApiResponse<{ loan: any }>> {
    const response = await this.api.post('/api/v1/loans', loanData)
    return response.data
  }

  async updateLoan(id: number, loanData: any): Promise<ApiResponse<{ loan: any }>> {
    const response = await this.api.put(`/api/v1/loans/${id}`, loanData)
    return response.data
  }

  async approveLoan(id: number): Promise<ApiResponse<{ loan: any }>> {
    const response = await this.api.post(`/api/v1/loans/${id}/approve`)
    return response.data
  }

  async rejectLoan(id: number, reason: string): Promise<ApiResponse<{ loan: any }>> {
    const response = await this.api.post(`/api/v1/loans/${id}/reject`, { reason })
    return response.data
  }

  async disburseLoan(id: number): Promise<ApiResponse<{ loan: any }>> {
    const response = await this.api.post(`/api/v1/loans/${id}/disburse`)
    return response.data
  }

  async getLoanPayments(id: number): Promise<ApiResponse<{ payments: any[] }>> {
    const response = await this.api.get(`/api/v1/loans/${id}/payments`)
    return response.data
  }

  async makePayment(id: number, paymentData: any): Promise<ApiResponse<any>> {
    const response = await this.api.post(`/api/v1/loans/${id}/payments`, paymentData)
    return response.data
  }

  async getPaymentSchedule(id: number): Promise<ApiResponse<{ schedule: any[] }>> {
    const response = await this.api.get(`/api/v1/loans/${id}/schedule`)
    return response.data
  }

  // Payments
  async getPayments(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await this.api.get('/api/v1/payments', { params })
    return response.data
  }

  async getPayment(id: number): Promise<ApiResponse<{ payment: any }>> {
    const response = await this.api.get(`/api/v1/payments/${id}`)
    return response.data
  }

  async initiateMpesaPayment(paymentData: any): Promise<ApiResponse<any>> {
    const response = await this.api.post('/api/v1/payments/mpesa/initiate', paymentData)
    return response.data
  }

  async initiateAirtelPayment(paymentData: any): Promise<ApiResponse<any>> {
    const response = await this.api.post('/api/v1/payments/airtel/initiate', paymentData)
    return response.data
  }

  // Asset Sales
  async getAssetSales(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await this.api.get('/api/v1/sales', { params })
    return response.data
  }

  async getAssetSale(id: number): Promise<ApiResponse<{ sale: any }>> {
    const response = await this.api.get(`/api/v1/sales/${id}`)
    return response.data
  }

  async createAssetSale(saleData: any): Promise<ApiResponse<{ sale: any }>> {
    const response = await this.api.post('/api/v1/sales', saleData)
    return response.data
  }

  async updateAssetSale(id: number, saleData: any): Promise<ApiResponse<{ sale: any }>> {
    const response = await this.api.put(`/api/v1/sales/${id}`, saleData)
    return response.data
  }

  async cancelAssetSale(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.delete(`/api/v1/sales/${id}`)
    return response.data
  }

  async placeBid(id: number, bidData: any): Promise<ApiResponse<any>> {
    const response = await this.api.post(`/api/v1/sales/${id}/bids`, bidData)
    return response.data
  }

  async getSaleBids(id: number): Promise<ApiResponse<{ bids: any[] }>> {
    const response = await this.api.get(`/api/v1/sales/${id}/bids`)
    return response.data
  }

  // Co-ownership
  async getCoOwnerships(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await this.api.get('/api/v1/co-ownership', { params })
    return response.data
  }

  async getCoOwnership(id: number): Promise<ApiResponse<{ coOwnership: any }>> {
    const response = await this.api.get(`/api/v1/co-ownership/${id}`)
    return response.data
  }

  // Notifications
  async getNotifications(): Promise<ApiResponse<any[]>> {
    const response = await this.api.get('/api/v1/notifications')
    return response.data
  }

  async markNotificationAsRead(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.put(`/api/v1/notifications/${id}/read`)
    return response.data
  }

  async markAllNotificationsAsRead(): Promise<ApiResponse<any>> {
    const response = await this.api.put('/api/v1/notifications/read-all')
    return response.data
  }

  // Generic methods
  async get<T>(url: string, params?: any): Promise<AxiosResponse<T>> {
    return this.api.get(url, { params })
  }

  async post<T>(url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.api.post(url, data)
  }

  async put<T>(url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.api.put(url, data)
  }

  async delete<T>(url: string): Promise<AxiosResponse<T>> {
    return this.api.delete(url)
  }
}

export const apiService = new ApiService()
export default apiService
