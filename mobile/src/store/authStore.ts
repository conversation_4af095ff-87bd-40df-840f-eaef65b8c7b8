import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { User, LoginForm, RegisterForm } from '@/types'
import apiService from '@/services/api'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  login: (credentials: LoginForm) => Promise<boolean>
  register: (userData: RegisterForm) => Promise<boolean>
  logout: () => Promise<void>
  getCurrentUser: () => Promise<void>
  clearError: () => void
  updateUser: (userData: Partial<User>) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (credentials: LoginForm) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await apiService.login(credentials.email, credentials.password)
          
          if (response.success && response.data) {
            const { user } = response.data
            
            set({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
            return true
          } else {
            set({
              isLoading: false,
              error: response.message || 'Login failed',
              isAuthenticated: false,
              user: null,
            })
            return false
          }
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'Login failed'
          set({
            isLoading: false,
            error: errorMessage,
            isAuthenticated: false,
            user: null,
          })
          return false
        }
      },

      register: async (userData: RegisterForm) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await apiService.register(userData)
          
          if (response.success && response.data) {
            const { user } = response.data
            
            set({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
            return true
          } else {
            set({
              isLoading: false,
              error: response.message || 'Registration failed',
              isAuthenticated: false,
              user: null,
            })
            return false
          }
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'Registration failed'
          set({
            isLoading: false,
            error: errorMessage,
            isAuthenticated: false,
            user: null,
          })
          return false
        }
      },

      logout: async () => {
        set({ isLoading: true })
        
        try {
          await apiService.logout()
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          })
        }
      },

      getCurrentUser: async () => {
        set({ isLoading: true })
        
        try {
          const response = await apiService.getCurrentUser()
          
          if (response.success && response.data) {
            const { user } = response.data
            
            set({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
          } else {
            await get().logout()
          }
        } catch (error: any) {
          console.error('Get current user error:', error)
          await get().logout()
        }
      },

      clearError: () => {
        set({ error: null })
      },

      updateUser: (userData: Partial<User>) => {
        const currentUser = get().user
        if (currentUser) {
          set({
            user: { ...currentUser, ...userData },
          })
        }
      },
    }),
    {
      name: 'hakimali-auth',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
