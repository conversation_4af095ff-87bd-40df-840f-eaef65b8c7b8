{"name": "hakimali-mobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"expo": "~49.0.15", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "@expo/vector-icons": "^13.0.0", "expo-font": "~11.4.0", "expo-splash-screen": "~0.20.5", "expo-constants": "~14.4.2", "expo-linking": "~5.0.2", "expo-router": "^2.0.0", "expo-system-ui": "~2.4.0", "expo-web-browser": "~12.3.2", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "@react-native-async-storage/async-storage": "1.18.2", "react-query": "^3.39.3", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "react-native-toast-message": "^2.1.6", "react-native-modal": "^13.0.1", "react-native-image-picker": "^7.0.3", "react-native-document-picker": "^9.1.1", "react-native-permissions": "^3.10.1", "expo-camera": "~13.4.4", "expo-media-library": "~15.4.1", "expo-image-picker": "~14.3.2", "expo-document-picker": "~11.5.4", "expo-file-system": "~15.4.5", "expo-location": "~16.1.0", "expo-contacts": "~12.3.0", "expo-sms": "~11.3.0", "expo-mail-composer": "~12.3.0", "expo-notifications": "~0.20.1", "expo-device": "~5.4.0", "expo-application": "~5.3.0", "expo-crypto": "~12.4.1", "expo-secure-store": "~12.3.1", "expo-local-authentication": "~13.4.1", "react-native-svg": "13.9.0", "react-native-chart-kit": "^6.12.0", "react-native-calendars": "^1.1302.0", "react-native-maps": "1.7.1", "react-native-webview": "13.2.2", "zustand": "^4.4.7", "date-fns": "^2.30.0", "react-native-super-grid": "^4.9.0", "react-native-paper": "^5.11.3", "react-native-vector-icons": "^10.0.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.56.0", "eslint-config-expo": "^7.0.0", "jest": "^29.2.1", "jest-expo": "~49.0.0", "react-test-renderer": "18.2.0", "typescript": "^5.1.3"}, "private": true}