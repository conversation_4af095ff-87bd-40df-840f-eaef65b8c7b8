import React from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function App() {
  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>HakiMali</Text>
        <Text style={styles.headerSubtitle}>Fair Asset Financing</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeTitle}>Welcome to HakiMali</Text>
          <Text style={styles.welcomeText}>
            Your trusted platform for fair asset-backed financing with innovative co-ownership models.
          </Text>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionGrid}>
            <TouchableOpacity style={[styles.actionCard, styles.primaryAction]}>
              <Text style={styles.actionIcon}>💰</Text>
              <Text style={styles.actionTitle}>Apply for Loan</Text>
              <Text style={styles.actionSubtitle}>Use your assets as collateral</Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.actionCard, styles.secondaryAction]}>
              <Text style={styles.actionIcon}>📱</Text>
              <Text style={styles.actionTitle}>Make Payment</Text>
              <Text style={styles.actionSubtitle}>Pay via M-Pesa or Airtel</Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.actionCard, styles.secondaryAction]}>
              <Text style={styles.actionIcon}>🏠</Text>
              <Text style={styles.actionTitle}>My Assets</Text>
              <Text style={styles.actionSubtitle}>Manage your assets</Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.actionCard, styles.secondaryAction]}>
              <Text style={styles.actionIcon}>📊</Text>
              <Text style={styles.actionTitle}>Loan Status</Text>
              <Text style={styles.actionSubtitle}>Track your loans</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Features Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Why Choose HakiMali?</Text>
          
          <View style={styles.featureCard}>
            <Text style={styles.featureIcon}>🤝</Text>
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>Co-Ownership Model</Text>
              <Text style={styles.featureText}>
                Build equity as you pay. When you reach 50% repayment, you become a co-owner of your asset.
              </Text>
            </View>
          </View>

          <View style={styles.featureCard}>
            <Text style={styles.featureIcon}>⚡</Text>
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>Instant Payments</Text>
              <Text style={styles.featureText}>
                Pay your loans instantly using M-Pesa, Airtel Money, or bank transfers.
              </Text>
            </View>
          </View>

          <View style={styles.featureCard}>
            <Text style={styles.featureIcon}>🛡️</Text>
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>Secure & Transparent</Text>
              <Text style={styles.featureText}>
                Bank-level security with full transparency in all transactions and agreements.
              </Text>
            </View>
          </View>
        </View>

        {/* Development Info */}
        <View style={styles.devSection}>
          <Text style={styles.devTitle}>🚀 Development Mode</Text>
          <Text style={styles.devText}>
            This is the HakiMali mobile app running in development mode with Expo.
          </Text>
          <Text style={styles.devText}>
            Backend API: http://localhost:3333
          </Text>
          <Text style={styles.devText}>
            Admin Panel: http://localhost:3000
          </Text>
          <Text style={styles.devText}>
            M-Pesa Mini App: http://localhost:3001
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    backgroundColor: '#0ea5e9',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#bae6fd',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  welcomeSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginTop: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 10,
  },
  welcomeText: {
    fontSize: 16,
    color: '#6b7280',
    lineHeight: 24,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 15,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: '48%',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  primaryAction: {
    backgroundColor: '#0ea5e9',
  },
  secondaryAction: {
    backgroundColor: 'white',
  },
  actionIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
    textAlign: 'center',
  },
  actionSubtitle: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
  },
  featureCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  featureIcon: {
    fontSize: 24,
    marginRight: 12,
    marginTop: 2,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  featureText: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  devSection: {
    backgroundColor: '#f3f4f6',
    borderRadius: 12,
    padding: 16,
    marginBottom: 30,
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  devTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 8,
  },
  devText: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
});
