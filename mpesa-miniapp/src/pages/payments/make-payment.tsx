import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { useForm } from 'react-hook-form'
import {
  ArrowLeftIcon,
  CreditCardIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
} from '@heroicons/react/24/outline'

interface PaymentForm {
  loanId: string
  amount: number
  phoneNumber: string
  paymentType: 'full' | 'partial' | 'minimum'
}

interface LoanDetails {
  id: number
  loanNumber: string
  asset: {
    name: string
    type: string
  }
  outstandingBalance: number
  monthlyPayment: number
  nextPaymentDate: string
  minimumPayment: number
}

const MakePaymentPage: React.FC = () => {
  const router = useRouter()
  const { loanId } = router.query
  const [isLoading, setIsLoading] = useState(false)
  const [paymentStep, setPaymentStep] = useState<'form' | 'confirm' | 'processing' | 'success' | 'error'>('form')
  const [loanDetails, setLoanDetails] = useState<LoanDetails | null>(null)
  const [transactionId, setTransactionId] = useState<string>('')

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm<PaymentForm>({
    defaultValues: {
      phoneNumber: '+254',
      paymentType: 'minimum',
    },
  })

  const watchedPaymentType = watch('paymentType')
  const watchedAmount = watch('amount')

  // Mock loan details - in real app, this would come from API
  useEffect(() => {
    if (loanId) {
      // Simulate API call
      setLoanDetails({
        id: Number(loanId),
        loanNumber: 'HM001234',
        asset: {
          name: 'Toyota Vitz 2018',
          type: 'vehicle'
        },
        outstandingBalance: 450000,
        monthlyPayment: 25000,
        nextPaymentDate: '2024-02-15',
        minimumPayment: 12500,
      })
    }
  }, [loanId])

  // Update amount based on payment type
  useEffect(() => {
    if (loanDetails) {
      switch (watchedPaymentType) {
        case 'minimum':
          setValue('amount', loanDetails.minimumPayment)
          break
        case 'full':
          setValue('amount', loanDetails.monthlyPayment)
          break
        case 'partial':
          setValue('amount', 0)
          break
      }
    }
  }, [watchedPaymentType, loanDetails, setValue])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const onSubmit = async (data: PaymentForm) => {
    setPaymentStep('confirm')
  }

  const confirmPayment = async () => {
    setPaymentStep('processing')
    setIsLoading(true)

    try {
      // Simulate M-Pesa STK push
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // Simulate successful payment
      setTransactionId('MPX' + Math.random().toString(36).substr(2, 9).toUpperCase())
      setPaymentStep('success')
    } catch (error) {
      setPaymentStep('error')
    } finally {
      setIsLoading(false)
    }
  }

  const goBack = () => {
    if (paymentStep === 'confirm') {
      setPaymentStep('form')
    } else {
      router.back()
    }
  }

  if (!loanDetails) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (paymentStep === 'success') {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white p-4 shadow-sm">
          <button onClick={() => router.push('/')} className="flex items-center text-blue-600">
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Back to Home
          </button>
        </div>

        <div className="p-4 flex items-center justify-center min-h-[calc(100vh-80px)]">
          <div className="bg-white rounded-lg p-6 shadow-lg text-center max-w-sm w-full">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircleIcon className="w-10 h-10 text-green-600" />
            </div>
            <h2 className="text-xl font-bold text-gray-900 mb-2">Payment Successful!</h2>
            <p className="text-gray-600 mb-4">
              Your payment of {formatCurrency(watchedAmount)} has been processed successfully.
            </p>
            <div className="bg-gray-50 rounded-lg p-3 mb-4">
              <p className="text-sm text-gray-600">Transaction ID</p>
              <p className="font-mono text-sm font-medium">{transactionId}</p>
            </div>
            <button
              onClick={() => router.push('/')}
              className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Done
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (paymentStep === 'error') {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white p-4 shadow-sm">
          <button onClick={goBack} className="flex items-center text-blue-600">
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Back
          </button>
        </div>

        <div className="p-4 flex items-center justify-center min-h-[calc(100vh-80px)]">
          <div className="bg-white rounded-lg p-6 shadow-lg text-center max-w-sm w-full">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <ExclamationCircleIcon className="w-10 h-10 text-red-600" />
            </div>
            <h2 className="text-xl font-bold text-gray-900 mb-2">Payment Failed</h2>
            <p className="text-gray-600 mb-4">
              We couldn't process your payment. Please try again or contact support.
            </p>
            <div className="space-y-2">
              <button
                onClick={() => setPaymentStep('form')}
                className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
              <button
                onClick={() => router.push('/')}
                className="w-full bg-gray-200 text-gray-800 py-3 rounded-lg font-medium hover:bg-gray-300 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (paymentStep === 'processing') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg p-6 shadow-lg text-center max-w-sm w-full">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">Processing Payment</h2>
          <p className="text-gray-600">
            Please check your phone for the M-Pesa prompt and enter your PIN to complete the payment.
          </p>
        </div>
      </div>
    )
  }

  if (paymentStep === 'confirm') {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white p-4 shadow-sm">
          <button onClick={goBack} className="flex items-center text-blue-600">
            <ArrowLeftIcon className="w-5 h-5 mr-2" />
            Back
          </button>
        </div>

        <div className="p-4">
          <h1 className="text-xl font-bold text-gray-900 mb-6">Confirm Payment</h1>

          <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 mb-6">
            <h2 className="font-semibold text-gray-900 mb-3">Payment Details</h2>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Loan</span>
                <span className="font-medium">{loanDetails.loanNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Asset</span>
                <span className="font-medium">{loanDetails.asset.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Amount</span>
                <span className="font-medium text-lg">{formatCurrency(watchedAmount)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Phone Number</span>
                <span className="font-medium">{watch('phoneNumber')}</span>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200 mb-6">
            <p className="text-blue-800 text-sm">
              You will receive an M-Pesa prompt on your phone. Enter your PIN to complete the payment.
            </p>
          </div>

          <button
            onClick={confirmPayment}
            disabled={isLoading}
            className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {isLoading ? 'Processing...' : 'Confirm Payment'}
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white p-4 shadow-sm">
        <button onClick={goBack} className="flex items-center text-blue-600">
          <ArrowLeftIcon className="w-5 h-5 mr-2" />
          Back
        </button>
      </div>

      <div className="p-4">
        <h1 className="text-xl font-bold text-gray-900 mb-6">Make Payment</h1>

        {/* Loan Details */}
        <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 mb-6">
          <div className="flex items-center mb-3">
            <CreditCardIcon className="w-6 h-6 text-blue-600 mr-2" />
            <h2 className="font-semibold text-gray-900">Loan Details</h2>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600">Loan Number</span>
              <span className="font-medium">{loanDetails.loanNumber}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Asset</span>
              <span className="font-medium">{loanDetails.asset.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Outstanding Balance</span>
              <span className="font-medium">{formatCurrency(loanDetails.outstandingBalance)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Monthly Payment</span>
              <span className="font-medium">{formatCurrency(loanDetails.monthlyPayment)}</span>
            </div>
          </div>
        </div>

        {/* Payment Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Payment Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Payment Type</label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  {...register('paymentType')}
                  type="radio"
                  value="minimum"
                  className="mr-3"
                />
                <div className="flex-1">
                  <span className="font-medium">Minimum Payment</span>
                  <span className="block text-sm text-gray-500">
                    {formatCurrency(loanDetails.minimumPayment)}
                  </span>
                </div>
              </label>
              <label className="flex items-center">
                <input
                  {...register('paymentType')}
                  type="radio"
                  value="full"
                  className="mr-3"
                />
                <div className="flex-1">
                  <span className="font-medium">Full Monthly Payment</span>
                  <span className="block text-sm text-gray-500">
                    {formatCurrency(loanDetails.monthlyPayment)}
                  </span>
                </div>
              </label>
              <label className="flex items-center">
                <input
                  {...register('paymentType')}
                  type="radio"
                  value="partial"
                  className="mr-3"
                />
                <div className="flex-1">
                  <span className="font-medium">Custom Amount</span>
                  <span className="block text-sm text-gray-500">Enter your own amount</span>
                </div>
              </label>
            </div>
          </div>

          {/* Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Amount (KES)</label>
            <input
              {...register('amount', {
                required: 'Amount is required',
                min: { value: 100, message: 'Minimum amount is KES 100' },
                max: { value: loanDetails.outstandingBalance, message: 'Amount cannot exceed outstanding balance' },
              })}
              type="number"
              disabled={watchedPaymentType !== 'partial'}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
              placeholder="Enter amount"
            />
            {errors.amount && (
              <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
            )}
          </div>

          {/* Phone Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">M-Pesa Phone Number</label>
            <input
              {...register('phoneNumber', {
                required: 'Phone number is required',
                pattern: {
                  value: /^\+254[17]\d{8}$/,
                  message: 'Please enter a valid Kenyan phone number',
                },
              })}
              type="tel"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="+254712345678"
            />
            {errors.phoneNumber && (
              <p className="mt-1 text-sm text-red-600">{errors.phoneNumber.message}</p>
            )}
          </div>

          <button
            type="submit"
            disabled={!isValid}
            className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Continue
          </button>
        </form>
      </div>
    </div>
  )
}

export default MakePaymentPage
