import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import {
  CreditCardIcon,
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline'

interface QuickAction {
  id: string
  title: string
  description: string
  icon: React.ComponentType<any>
  color: string
  action: () => void
}

interface LoanSummary {
  id: number
  loanNumber: string
  outstandingBalance: number
  nextPaymentDate: string
  monthlyPayment: number
  status: string
  asset: {
    name: string
    type: string
  }
}

const MpesaMiniAppHome: React.FC = () => {
  const router = useRouter()
  const [phoneNumber, setPhoneNumber] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [loans, setLoans] = useState<LoanSummary[]>([])

  // Mock data - in real app, this would come from API
  useEffect(() => {
    // Simulate loading user's active loans
    setLoans([
      {
        id: 1,
        loanNumber: 'HM001234',
        outstandingBalance: 450000,
        nextPaymentDate: '2024-02-15',
        monthlyPayment: 25000,
        status: 'active',
        asset: {
          name: 'Toyota Vitz 2018',
          type: 'vehicle'
        }
      },
      {
        id: 2,
        loanNumber: 'HM001235',
        outstandingBalance: 180000,
        nextPaymentDate: '2024-02-20',
        monthlyPayment: 15000,
        status: 'active',
        asset: {
          name: 'Samsung Galaxy S21',
          type: 'electronics'
        }
      }
    ])
  }, [])

  const quickActions: QuickAction[] = [
    {
      id: 'make-payment',
      title: 'Make Payment',
      description: 'Pay your loan installment',
      icon: CurrencyDollarIcon,
      color: 'bg-green-500',
      action: () => router.push('/payments/make-payment'),
    },
    {
      id: 'view-loans',
      title: 'My Loans',
      description: 'View loan details',
      icon: CreditCardIcon,
      color: 'bg-blue-500',
      action: () => router.push('/loans'),
    },
    {
      id: 'my-assets',
      title: 'My Assets',
      description: 'Manage your assets',
      icon: BuildingOfficeIcon,
      color: 'bg-purple-500',
      action: () => router.push('/assets'),
    },
    {
      id: 'payment-history',
      title: 'Payment History',
      description: 'View past payments',
      icon: ClockIcon,
      color: 'bg-orange-500',
      action: () => router.push('/payments/history'),
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100'
      case 'overdue':
        return 'text-red-600 bg-red-100'
      case 'pending':
        return 'text-yellow-600 bg-yellow-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-KE', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold">HakiMali</h1>
            <p className="text-blue-100 text-sm">Fair Asset Financing</p>
          </div>
          <div className="text-right">
            <p className="text-sm text-blue-100">Welcome back!</p>
            <p className="font-medium">+254 7XX XXX XXX</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-3">Quick Actions</h2>
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action) => (
            <button
              key={action.id}
              onClick={action.action}
              className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div className={`w-10 h-10 rounded-lg ${action.color} flex items-center justify-center mb-2`}>
                <action.icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-medium text-gray-900 text-sm">{action.title}</h3>
              <p className="text-xs text-gray-500 mt-1">{action.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Active Loans */}
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold text-gray-900">Active Loans</h2>
          <button
            onClick={() => router.push('/loans')}
            className="text-blue-600 text-sm font-medium"
          >
            View All
          </button>
        </div>

        {loans.length > 0 ? (
          <div className="space-y-3">
            {loans.map((loan) => (
              <div key={loan.id} className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h3 className="font-medium text-gray-900">{loan.asset.name}</h3>
                    <p className="text-sm text-gray-500">Loan #{loan.loanNumber}</p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(loan.status)}`}>
                    {loan.status.charAt(0).toUpperCase() + loan.status.slice(1)}
                  </span>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Outstanding Balance</p>
                    <p className="font-semibold text-gray-900">{formatCurrency(loan.outstandingBalance)}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Monthly Payment</p>
                    <p className="font-semibold text-gray-900">{formatCurrency(loan.monthlyPayment)}</p>
                  </div>
                </div>

                <div className="mt-3 pt-3 border-t border-gray-100">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs text-gray-500">Next Payment Due</p>
                      <p className="text-sm font-medium text-gray-900">{formatDate(loan.nextPaymentDate)}</p>
                    </div>
                    <button
                      onClick={() => router.push(`/payments/make-payment?loanId=${loan.id}`)}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                    >
                      Pay Now
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 text-center">
            <CreditCardIcon className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900 mb-1">No Active Loans</h3>
            <p className="text-sm text-gray-500 mb-4">You don't have any active loans at the moment.</p>
            <button
              onClick={() => router.push('/loans/apply')}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
            >
              Apply for Loan
            </button>
          </div>
        )}
      </div>

      {/* Recent Activity */}
      <div className="p-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-3">Recent Activity</h2>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircleIcon className="w-5 h-5 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Payment Successful</p>
                <p className="text-xs text-gray-500">KES 25,000 • Loan #HM001234 • 2 days ago</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <CreditCardIcon className="w-5 h-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Loan Approved</p>
                <p className="text-xs text-gray-500">Samsung Galaxy S21 • 1 week ago</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">Payment Reminder</p>
                <p className="text-xs text-gray-500">Due in 3 days • KES 15,000 • 2 weeks ago</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 pb-8">
        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-white text-sm font-bold">H</span>
            </div>
            <div>
              <h3 className="font-medium text-blue-900">Need Help?</h3>
              <p className="text-sm text-blue-700 mt-1">
                Contact our support team for assistance with your loans and payments.
              </p>
              <button className="text-blue-600 text-sm font-medium mt-2">
                Get Support →
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MpesaMiniAppHome
