{"name": "hakimali-mpesa-miniapp", "version": "1.0.0", "description": "HakiMali M-Pesa Mini App for quick loan payments and asset management", "main": "index.js", "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "axios": "^1.6.2", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "clsx": "^2.0.0", "zustand": "^4.4.7", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "prettier": "^3.1.1"}, "keywords": ["mpesa", "miniapp", "hakim<PERSON>", "loans", "payments", "kenya"], "author": "HakiMali Team", "license": "MIT"}