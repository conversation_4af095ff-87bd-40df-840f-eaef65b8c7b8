/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333',
    NEXT_PUBLIC_MPESA_SHORTCODE: process.env.NEXT_PUBLIC_MPESA_SHORTCODE,
  },
  images: {
    domains: ['localhost', 'hakimali.com'],
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL}/api/:path*`,
      },
    ]
  },
  // Optimize for M-Pesa mini app constraints
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['@heroicons/react'],
  },
  // Ensure compatibility with M-Pesa WebView
  trailingSlash: true,
  output: 'standalone',
}

module.exports = nextConfig
