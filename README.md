# HakiMali - Fair Asset Financing and Co-Ownership Platform

HakiMali is a mobile-first application designed to revolutionize asset financing in Kenya by introducing a fair co-ownership and repossession model. The platform protects borrowers who have paid a significant portion of their loans but risk losing their entire asset upon repossession.

## Project Structure

```
hakimali/
├── backend/                 # AdonisJS Backend API
├── mobile/                  # React Native Mobile App
├── admin-panel/            # Web Admin Panel
├── mpesa-miniapp/          # M-Pesa Mini App
├── docs/                   # Documentation
├── scripts/                # Deployment and utility scripts
└── docker-compose.yml      # Development environment
```

## Core Features

- **Co-ownership Model**: Automatic co-ownership when 50%+ of loan is paid
- **Fair Repossession**: Borrowers receive their equity share from asset sales
- **Borrower-Initiated Sales**: Proactive asset selling to avoid repossession
- **Multi-Platform**: Native mobile apps, web admin, and M-Pesa mini app
- **Secure Payments**: Integration with M-Pesa, Airtel Money, and banks
- **Regulatory Compliance**: Adheres to CBK guidelines and Kenyan regulations

## Technology Stack

- **Backend**: AdonisJS (Node.js)
- **Mobile**: React Native (Expo)
- **Admin Panel**: React.js
- **Database**: PostgreSQL
- **Payments**: M-Pesa API, Airtel Money
- **Cloud**: AWS/Azure
- **Authentication**: JWT with MFA

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL 14+
- React Native development environment
- Expo CLI

### Development Setup

1. Clone the repository
2. Install dependencies for each module
3. Set up environment variables
4. Run database migrations
5. Start development servers

See individual module README files for detailed setup instructions.

## License

Proprietary - All rights reserved

docker-compose up -d
docker-compose down
app key = node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Generate new JWT secret:
openssl rand -hex 32

# Generate encryption key (32 characters):
openssl rand -hex 16