version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: hakimali-postgres
    environment:
      POSTGRES_DB: hakimali_dev
      POSTGRES_USER: hakimali
      POSTGRES_PASSWORD: hakimali_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - hakimali-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: hakimali-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - hakimali-network

  # AdonisJS Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: hakimali-backend
    environment:
      NODE_ENV: development
      DB_CONNECTION: pg
      PG_HOST: postgres
      PG_PORT: 5432
      PG_USER: hakimali
      PG_PASSWORD: hakimali_password
      PG_DB_NAME: hakimali_dev
      REDIS_CONNECTION: redis
      REDIS_HOST: redis
      REDIS_PORT: 6379
    ports:
      - "3333:3333"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - hakimali-network
    command: npm run dev

  # Admin Panel (Next.js)
  admin-panel:
    build:
      context: ./admin-panel
      dockerfile: Dockerfile.dev
    container_name: hakimali-admin
    environment:
      NEXT_PUBLIC_API_URL: http://api.hakimali.local
      NODE_ENV: development
    ports:
      - "3000:3000"
    volumes:
      - ./admin-panel:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - hakimali-network
    restart: unless-stopped

  # M-Pesa Mini App (Next.js)
  mpesa-miniapp:
    build:
      context: ./mpesa-miniapp
      dockerfile: Dockerfile.dev
    container_name: hakimali-mpesa
    environment:
      NEXT_PUBLIC_API_URL: http://api.hakimali.local
      NEXT_PUBLIC_MPESA_SHORTCODE: your-shortcode
      NODE_ENV: development
    ports:
      - "3001:3001"
    volumes:
      - ./mpesa-miniapp:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - hakimali-network
    restart: unless-stopped

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: hakimali-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - backend
      - admin-panel
      - mpesa-miniapp
    networks:
      - hakimali-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local
  nginx_logs:
    driver: local

networks:
  hakimali-network:
    driver: bridge
