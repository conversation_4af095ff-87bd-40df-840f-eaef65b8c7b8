{"name": "hakimali-platform", "version": "1.0.0", "description": "Fair Asset Financing and Co-Ownership Platform", "private": true, "workspaces": ["backend", "mobile", "admin-panel", "mpesa-miniapp"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:mobile\" \"npm run dev:admin\"", "dev:backend": "cd backend && npm run dev", "dev:mobile": "cd mobile && npm start", "dev:admin": "cd admin-panel && npm start", "build": "npm run build:backend && npm run build:admin", "build:backend": "cd backend && npm run build", "build:admin": "cd admin-panel && npm run build", "test": "npm run test:backend && npm run test:mobile && npm run test:admin", "test:backend": "cd backend && npm test", "test:mobile": "cd mobile && npm test", "test:admin": "cd admin-panel && npm test", "lint": "npm run lint:backend && npm run lint:mobile && npm run lint:admin", "lint:backend": "cd backend && npm run lint", "lint:mobile": "cd mobile && npm run lint", "lint:admin": "cd admin-panel && npm run lint", "setup": "npm install && npm run setup:backend && npm run setup:mobile && npm run setup:admin", "setup:backend": "cd backend && npm install", "setup:mobile": "cd mobile && npm install", "setup:admin": "cd admin-panel && npm install"}, "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}