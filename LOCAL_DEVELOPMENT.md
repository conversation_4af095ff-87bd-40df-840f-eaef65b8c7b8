# 🏠 HakiMali Local Development Guide

This guide will help you run HakiMali locally without <PERSON><PERSON> for development.

## ✅ Prerequisites

- **Node.js 18+** (check with `node -v`)
- **npm** (check with `npm -v`)
- **PostgreSQL** (for database)
- **Redis** (for caching and sessions)

## 🚀 Quick Setup

### Step 1: Run the Local Setup Script

```bash
# Make the script executable and run it
chmod +x setup-local.sh
./setup-local.sh
```

This script will:
- ✅ Check Node.js and npm versions
- ✅ Clean npm cache and existing installations
- ✅ Install dependencies for all components
- ✅ Create development scripts
- ✅ Set up directory structure

### Step 2: Set Up Databases

#### PostgreSQL Setup
```bash
# Install PostgreSQL (Ubuntu/Debian)
sudo apt update
sudo apt install postgresql postgresql-contrib

# Start PostgreSQL service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres createdb hakimali_dev
sudo -u postgres psql -c "CREATE USER hakimali WITH PASSWORD 'hakimali_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE hakimali_dev TO hakimali;"
```

#### Redis Setup
```bash
# Install Redis (Ubuntu/Debian)
sudo apt update
sudo apt install redis-server

# Start Redis service
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Test Redis connection
redis-cli ping
```

### Step 3: Update Environment Variables

The environment files should already be configured. Update them if needed:

#### `backend/.env`
```env
# Database (should already be set)
PG_HOST=localhost
PG_PORT=5432
PG_USER=hakimali
PG_PASSWORD=hakimali_password
PG_DB_NAME=hakimali_dev

# Redis (should already be set)
REDIS_HOST=localhost
REDIS_PORT=6379
```

### Step 4: Start Development

#### Option A: Start All Services at Once
```bash
./start-all-local.sh
```

#### Option B: Start Services Individually
```bash
# Terminal 1: Backend API
./start-backend.sh

# Terminal 2: Admin Panel
./start-admin.sh

# Terminal 3: M-Pesa Mini App
./start-mpesa.sh

# Terminal 4: Mobile App (optional)
./start-mobile.sh
```

### Step 5: Access Applications

- **Backend API**: http://localhost:3333
- **Admin Panel**: http://localhost:3000
- **M-Pesa Mini App**: http://localhost:3001

## 🛠️ Manual Setup (if script fails)

### 1. Clean and Install Dependencies

```bash
# Clean npm cache
npm cache clean --force

# Backend
cd backend
rm -rf node_modules package-lock.json
npm install
cd ..

# Admin Panel
cd admin-panel
rm -rf node_modules package-lock.json
npm install
cd ..

# M-Pesa Mini App
cd mpesa-miniapp
rm -rf node_modules package-lock.json
npm install
cd ..

# Mobile App (optional)
cd mobile
rm -rf node_modules package-lock.json
npm install
cd ..
```

### 2. Start Services Manually

```bash
# Backend (Terminal 1)
cd backend
npm run dev

# Admin Panel (Terminal 2)
cd admin-panel
npm run dev

# M-Pesa Mini App (Terminal 3)
cd mpesa-miniapp
npm run dev
```

## 🔧 Development Commands

### Backend (AdonisJS)
```bash
cd backend

# Start development server
npm run dev

# Run migrations
npm run migration:run

# Create new migration
npm run make:migration create_table_name

# Seed database
npm run db:seed

# Run tests
npm test
```

### Admin Panel (Next.js)
```bash
cd admin-panel

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run linting
npm run lint
```

### M-Pesa Mini App (Next.js)
```bash
cd mpesa-miniapp

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### Mobile App (Expo)
```bash
cd mobile

# Start Expo development server
npm start

# Run on iOS simulator
npm run ios

# Run on Android emulator
npm run android
```

## 🔍 Troubleshooting

### Port Conflicts
If ports are already in use:
```bash
# Check what's using the ports
lsof -i :3333  # Backend
lsof -i :3000  # Admin Panel
lsof -i :3001  # M-Pesa Mini App

# Kill processes using the ports
sudo kill -9 $(lsof -ti:3333)
sudo kill -9 $(lsof -ti:3000)
sudo kill -9 $(lsof -ti:3001)
```

### Database Connection Issues
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check if database exists
psql -U hakimali -d hakimali_dev -c "\l"

# Reset database
sudo -u postgres dropdb hakimali_dev
sudo -u postgres createdb hakimali_dev
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE hakimali_dev TO hakimali;"
```

### Redis Connection Issues
```bash
# Check Redis status
sudo systemctl status redis-server

# Test Redis connection
redis-cli ping

# Restart Redis
sudo systemctl restart redis-server
```

### npm Install Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Use different registry if needed
npm install --registry https://registry.npmjs.org/
```

## 🛑 Stopping Services

### Stop All Services
```bash
./stop-all-local.sh
```

### Stop Individual Services
```bash
# Find and kill processes
ps aux | grep node
kill -9 <PID>

# Or use the stop script for each service
# (Ctrl+C in each terminal)
```

## 📝 Development Tips

1. **Hot Reload**: All services support hot reload for development
2. **Environment Variables**: Update `.env` files as needed
3. **Database Changes**: Run migrations after pulling new code
4. **API Testing**: Use tools like Postman or curl to test APIs
5. **Logs**: Check console output in each terminal for debugging

## 🎯 Success Indicators

You'll know everything is working when:
- ✅ Backend API responds at http://localhost:3333
- ✅ Admin panel loads at http://localhost:3000
- ✅ M-Pesa mini app loads at http://localhost:3001
- ✅ No error messages in terminal outputs
- ✅ Database connections are successful

## 🆘 Getting Help

If you encounter issues:
1. Check the terminal output for error messages
2. Verify database and Redis are running
3. Ensure all environment variables are set correctly
4. Try cleaning and reinstalling dependencies
5. Check port conflicts

Happy local development with HakiMali! 🚀
