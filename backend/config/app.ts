import { defineConfig } from '@adonisjs/core/app'
import env from '#start/env'

export default defineConfig({
  /*
  |--------------------------------------------------------------------------
  | App Key
  |--------------------------------------------------------------------------
  |
  | The app key is used for encrypting cookies, generating signed URLs,
  | and by the "encryption" module.
  |
  | The `appKey` must be a secure random key. You can generate a new
  | app key using the `node ace generate:key` command.
  |
  */
  appKey: env.get('APP_KEY'),

  /*
  |--------------------------------------------------------------------------
  | HTTP
  |--------------------------------------------------------------------------
  |
  | Configuration for the HTTP server
  |
  */
  http: {
    /*
    |--------------------------------------------------------------------------
    | Allow method spoofing
    |--------------------------------------------------------------------------
    |
    | Method spoofing enables defining custom HTTP verbs using the "_method"
    | query string or form field.
    |
    */
    allowMethodSpoofing: false,

    /*
    |--------------------------------------------------------------------------
    | Generate request id
    |--------------------------------------------------------------------------
    |
    | Whether or not to generate a unique request id for each HTTP request. The
    | request id is available using "ctx.request.id()" method.
    |
    */
    generateRequestId: true,

    /*
    |--------------------------------------------------------------------------
    | Trusty proxy
    |--------------------------------------------------------------------------
    |
    | Define the proxy servers that AdonisJS must trust for reading the
    | "X-Forwarded" headers.
    |
    */
    trustProxy: false,

    /*
    |--------------------------------------------------------------------------
    | JSONP Callback
    |--------------------------------------------------------------------------
    |
    | Define the query string parameter name for making JSONP requests.
    |
    */
    jsonpCallbackName: 'callback',

    /*
    |--------------------------------------------------------------------------
    | Cookie settings
    |--------------------------------------------------------------------------
    |
    | Settings for HTTP cookies
    |
    */
    cookie: {
      domain: '',
      path: '/',
      maxAge: '2h',
      httpOnly: true,
      secure: false,
      sameSite: false,
    },
  },

  /*
  |--------------------------------------------------------------------------
  | Assets
  |--------------------------------------------------------------------------
  |
  | Configuration for serving static assets
  |
  */
  assets: {
    /*
    |--------------------------------------------------------------------------
    | Enabled
    |--------------------------------------------------------------------------
    |
    | A boolean to enable or disable serving static files from the "public"
    | directory.
    |
    */
    enabled: true,

    /*
    |--------------------------------------------------------------------------
    | Etag
    |--------------------------------------------------------------------------
    |
    | Enable or disable etag generation
    |
    */
    etag: true,

    /*
    |--------------------------------------------------------------------------
    | Last modified
    |--------------------------------------------------------------------------
    |
    | Enable or disable "Last-Modified" header.
    |
    */
    lastModified: true,
  },

  /*
  |--------------------------------------------------------------------------
  | Logger
  |--------------------------------------------------------------------------
  |
  | Configuration for the logger used by your app
  |
  */
  logger: {
    /*
    |--------------------------------------------------------------------------
    | Default log level
    |--------------------------------------------------------------------------
    |
    | The default log level to use when no explicit level is defined for
    | a log message.
    |
    */
    level: env.get('LOG_LEVEL'),

    /*
    |--------------------------------------------------------------------------
    | Fake logger
    |--------------------------------------------------------------------------
    |
    | The fake logger can be used to silence all the log messages. You can
    | toggle it using the "LOG_LEVEL=silent" environment variable.
    |
    */
    enabled: true,
  },
})
