{"extends": "@adonisjs/tsconfig/app", "compilerOptions": {"outDir": "./build", "rootDir": "./", "sourceMap": true, "paths": {"#controllers/*": ["./app/Controllers/*"], "#exceptions/*": ["./app/Exceptions/*"], "#models/*": ["./app/Models/*"], "#mails/*": ["./app/Mails/*"], "#services/*": ["./app/Services/*"], "#listeners/*": ["./app/Listeners/*"], "#events/*": ["./app/Events/*"], "#middleware/*": ["./app/Middleware/*"], "#validators/*": ["./app/Validators/*"], "#start/*": ["./start/*"], "#tests/*": ["./tests/*"], "#database/*": ["./database/*"], "#config/*": ["./config/*"]}}, "ts-node": {"require": ["tsconfig-paths/register"]}}