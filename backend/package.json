{"name": "hakimali-backend", "version": "1.0.0", "private": true, "scripts": {"dev": "node ace serve --watch", "build": "node ace build --production", "start": "node server.js", "test": "node ace test", "lint": "eslint . --ext=.ts", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "eslintConfig": {"extends": ["@adonisjs/eslint-config/app"]}, "prettier": "@adonisjs/prettier-config", "devDependencies": {"@adonisjs/assembler": "^7.0.0", "@adonisjs/eslint-config": "^1.2.1", "@adonisjs/prettier-config": "^1.2.1", "@adonisjs/tsconfig": "^1.2.1", "@japa/assert": "^2.1.0", "@japa/runner": "^3.1.1", "@japa/api-client": "^2.0.2", "@swc/core": "^1.3.107", "@types/node": "^20.11.5", "eslint": "^8.56.0", "hot-hook": "^0.2.6", "pino-pretty": "^10.3.1", "prettier": "^3.2.4", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "dependencies": {"@adonisjs/core": "^6.2.0", "@adonisjs/cors": "^2.2.1", "@adonisjs/lucid": "^20.1.0", "@adonisjs/auth": "^9.1.1", "@adonisjs/session": "^7.1.1", "@adonisjs/shield": "^8.1.1", "@adonisjs/static": "^1.1.1", "@adonisjs/bodyparser": "^10.0.1", "@adonisjs/limiter": "^2.1.1", "@adonisjs/redis": "^9.2.0", "@adonisjs/mail": "^9.1.1", "@adonisjs/drive": "^3.1.0", "@vinejs/vine": "^3.0.1", "pg": "^8.11.3", "redis": "^4.6.12", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "axios": "^1.6.5", "uuid": "^9.0.1", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "sharp": "^0.33.2", "nodemailer": "^6.9.8", "twilio": "^4.20.1", "winston": "^3.11.0"}, "hotHook": {"boundaries": ["./app/Controllers/**/*.ts", "./app/Middleware/*.ts"]}}