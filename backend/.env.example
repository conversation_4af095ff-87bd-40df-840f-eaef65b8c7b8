TZ=UTC
PORT=3333
HOST=0.0.0.0
LOG_LEVEL=info
APP_KEY=your-app-key-here
NODE_ENV=development

# Database
DB_CONNECTION=pg
PG_HOST=localhost
PG_PORT=5432
PG_USER=hakimali
PG_PASSWORD=hakimali_password
PG_DB_NAME=hakimali_dev

# Redis
REDIS_CONNECTION=redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Session
SESSION_DRIVER=redis

# Authentication
JWT_SECRET=your-jwt-secret-here
JWT_EXPIRES_IN=7d

# M-Pesa Configuration
MPESA_CONSUMER_KEY=your-mpesa-consumer-key
MPESA_CONSUMER_SECRET=your-mpesa-consumer-secret
MPESA_SHORTCODE=your-shortcode
MPESA_PASSKEY=your-passkey
MPESA_ENVIRONMENT=sandbox
MPESA_CALLBACK_URL=https://yourdomain.com/api/mpesa/callback

# Airtel Money Configuration
AIRTEL_CLIENT_ID=your-airtel-client-id
AIRTEL_CLIENT_SECRET=your-airtel-client-secret
AIRTEL_ENVIRONMENT=sandbox

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone

# Email Configuration
MAIL_MAILER=smtp
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password

# File Storage
DRIVE_DISK=local
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_S3_BUCKET=
AWS_DEFAULT_REGION=

# KYC & Document Verification
KYC_API_KEY=your-kyc-api-key
KYC_API_URL=https://api.kyc-provider.com

# Credit Bureau Integration
CRB_API_KEY=your-crb-api-key
CRB_API_URL=https://api.creditbureau.co.ke

# Encryption
ENCRYPTION_KEY=your-encryption-key-here

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_DURATION=900

# Audit & Compliance
AUDIT_LOG_ENABLED=true
COMPLIANCE_MODE=strict
