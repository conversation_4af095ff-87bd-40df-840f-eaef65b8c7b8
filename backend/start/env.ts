/*
|--------------------------------------------------------------------------
| Environment variables service
|--------------------------------------------------------------------------
|
| The `Env.create` method creates an instance of the Env service. The
| service validates the environment variables and also cast values
| to JavaScript data types.
|
*/

import { Env } from '@adonisjs/core/env'

export default await Env.create(new URL('../', import.meta.url), {
  NODE_ENV: Env.schema.enum(['development', 'production', 'test'] as const),
  PORT: Env.schema.number(),
  APP_KEY: Env.schema.string(),
  HOST: Env.schema.string({ format: 'host' }),
  LOG_LEVEL: Env.schema.string(),

  /*
  |----------------------------------------------------------
  | Variables for configuring database connection
  |----------------------------------------------------------
  */
  DB_CONNECTION: Env.schema.string(),
  PG_HOST: Env.schema.string({ format: 'host' }),
  PG_PORT: Env.schema.number(),
  PG_USER: Env.schema.string(),
  PG_PASSWORD: Env.schema.string.optional(),
  PG_DB_NAME: Env.schema.string(),

  /*
  |----------------------------------------------------------
  | Variables for configuring Redis connection
  |----------------------------------------------------------
  */
  REDIS_CONNECTION: Env.schema.string(),
  REDIS_HOST: Env.schema.string({ format: 'host' }),
  REDIS_PORT: Env.schema.number(),
  REDIS_PASSWORD: Env.schema.string.optional(),

  /*
  |----------------------------------------------------------
  | Variables for configuring session
  |----------------------------------------------------------
  */
  SESSION_DRIVER: Env.schema.string(),

  /*
  |----------------------------------------------------------
  | Variables for JWT authentication
  |----------------------------------------------------------
  */
  JWT_SECRET: Env.schema.string(),
  JWT_EXPIRES_IN: Env.schema.string(),

  /*
  |----------------------------------------------------------
  | Variables for M-Pesa integration
  |----------------------------------------------------------
  */
  MPESA_CONSUMER_KEY: Env.schema.string(),
  MPESA_CONSUMER_SECRET: Env.schema.string(),
  MPESA_SHORTCODE: Env.schema.string(),
  MPESA_PASSKEY: Env.schema.string(),
  MPESA_ENVIRONMENT: Env.schema.enum(['sandbox', 'production'] as const),
  MPESA_CALLBACK_URL: Env.schema.string({ format: 'url' }),

  /*
  |----------------------------------------------------------
  | Variables for Airtel Money integration
  |----------------------------------------------------------
  */
  AIRTEL_CLIENT_ID: Env.schema.string(),
  AIRTEL_CLIENT_SECRET: Env.schema.string(),
  AIRTEL_ENVIRONMENT: Env.schema.enum(['sandbox', 'production'] as const),

  /*
  |----------------------------------------------------------
  | Variables for SMS (Twilio)
  |----------------------------------------------------------
  */
  TWILIO_ACCOUNT_SID: Env.schema.string(),
  TWILIO_AUTH_TOKEN: Env.schema.string(),
  TWILIO_PHONE_NUMBER: Env.schema.string(),

  /*
  |----------------------------------------------------------
  | Variables for email configuration
  |----------------------------------------------------------
  */
  MAIL_MAILER: Env.schema.string(),
  SMTP_HOST: Env.schema.string(),
  SMTP_PORT: Env.schema.number(),
  SMTP_USERNAME: Env.schema.string(),
  SMTP_PASSWORD: Env.schema.string(),

  /*
  |----------------------------------------------------------
  | Variables for file storage
  |----------------------------------------------------------
  */
  DRIVE_DISK: Env.schema.string(),
  AWS_ACCESS_KEY_ID: Env.schema.string.optional(),
  AWS_SECRET_ACCESS_KEY: Env.schema.string.optional(),
  AWS_S3_BUCKET: Env.schema.string.optional(),
  AWS_DEFAULT_REGION: Env.schema.string.optional(),

  /*
  |----------------------------------------------------------
  | Variables for KYC and compliance
  |----------------------------------------------------------
  */
  KYC_API_KEY: Env.schema.string(),
  KYC_API_URL: Env.schema.string({ format: 'url' }),
  CRB_API_KEY: Env.schema.string(),
  CRB_API_URL: Env.schema.string({ format: 'url' }),
  ENCRYPTION_KEY: Env.schema.string(),
  AUDIT_LOG_ENABLED: Env.schema.boolean(),
  COMPLIANCE_MODE: Env.schema.enum(['strict', 'normal'] as const),
})
