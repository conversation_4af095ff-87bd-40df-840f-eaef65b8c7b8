/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from './kernel.js'

// Health check
router.get('/health', async () => {
  return { status: 'ok', timestamp: new Date().toISOString() }
})

// API routes
router.group(() => {
  // Authentication routes
  router.group(() => {
    router.post('/register', '#controllers/auth_controller.register')
    router.post('/login', '#controllers/auth_controller.login')
    router.post('/logout', '#controllers/auth_controller.logout').use(middleware.auth())
    router.post('/refresh', '#controllers/auth_controller.refresh')
    router.post('/forgot-password', '#controllers/auth_controller.forgotPassword')
    router.post('/reset-password', '#controllers/auth_controller.resetPassword')
    router.post('/verify-email', '#controllers/auth_controller.verifyEmail')
    router.post('/resend-verification', '#controllers/auth_controller.resendVerification')
    router.post('/verify-phone', '#controllers/auth_controller.verifyPhone')
    router.post('/enable-2fa', '#controllers/auth_controller.enable2FA').use(middleware.auth())
    router.post('/verify-2fa', '#controllers/auth_controller.verify2FA').use(middleware.auth())
    router.post('/disable-2fa', '#controllers/auth_controller.disable2FA').use(middleware.auth())
  }).prefix('/auth')

  // User profile routes
  router.group(() => {
    router.get('/me', '#controllers/users_controller.me')
    router.put('/me', '#controllers/users_controller.updateProfile')
    router.post('/upload-documents', '#controllers/users_controller.uploadDocuments')
    router.get('/kyc-status', '#controllers/users_controller.kycStatus')
    router.post('/submit-kyc', '#controllers/users_controller.submitKyc')
  }).prefix('/users').use(middleware.auth())

  // Asset routes
  router.group(() => {
    router.get('/', '#controllers/assets_controller.index')
    router.post('/', '#controllers/assets_controller.store')
    router.get('/:id', '#controllers/assets_controller.show')
    router.put('/:id', '#controllers/assets_controller.update')
    router.delete('/:id', '#controllers/assets_controller.destroy')
    router.post('/:id/valuations', '#controllers/assets_controller.addValuation')
    router.get('/:id/valuations', '#controllers/assets_controller.getValuations')
    router.post('/:id/images', '#controllers/assets_controller.uploadImages')
    router.post('/:id/documents', '#controllers/assets_controller.uploadDocuments')
  }).prefix('/assets').use(middleware.auth())

  // Loan routes
  router.group(() => {
    router.get('/', '#controllers/loans_controller.index')
    router.post('/', '#controllers/loans_controller.store')
    router.get('/:id', '#controllers/loans_controller.show')
    router.put('/:id', '#controllers/loans_controller.update')
    router.post('/:id/approve', '#controllers/loans_controller.approve')
    router.post('/:id/reject', '#controllers/loans_controller.reject')
    router.post('/:id/disburse', '#controllers/loans_controller.disburse')
    router.get('/:id/payments', '#controllers/loans_controller.getPayments')
    router.post('/:id/payments', '#controllers/loans_controller.makePayment')
    router.get('/:id/schedule', '#controllers/loans_controller.getPaymentSchedule')
    router.post('/:id/default', '#controllers/loans_controller.markDefault')
  }).prefix('/loans').use(middleware.auth())

  // Payment routes
  router.group(() => {
    router.get('/', '#controllers/payments_controller.index')
    router.get('/:id', '#controllers/payments_controller.show')
    router.post('/mpesa/initiate', '#controllers/payments_controller.initiateMpesa')
    router.post('/mpesa/callback', '#controllers/payments_controller.mpesaCallback')
    router.post('/airtel/initiate', '#controllers/payments_controller.initiateAirtel')
    router.post('/airtel/callback', '#controllers/payments_controller.airtelCallback')
  }).prefix('/payments').use(middleware.auth())

  // Asset sales routes
  router.group(() => {
    router.get('/', '#controllers/asset_sales_controller.index')
    router.post('/', '#controllers/asset_sales_controller.store')
    router.get('/:id', '#controllers/asset_sales_controller.show')
    router.put('/:id', '#controllers/asset_sales_controller.update')
    router.delete('/:id', '#controllers/asset_sales_controller.cancel')
    router.post('/:id/bids', '#controllers/asset_sales_controller.placeBid')
    router.get('/:id/bids', '#controllers/asset_sales_controller.getBids')
    router.post('/:id/complete', '#controllers/asset_sales_controller.completeSale')
  }).prefix('/sales').use(middleware.auth())

  // Co-ownership routes
  router.group(() => {
    router.get('/', '#controllers/co_ownership_controller.index')
    router.get('/:id', '#controllers/co_ownership_controller.show')
    router.post('/:id/activate', '#controllers/co_ownership_controller.activate')
    router.post('/:id/dissolve', '#controllers/co_ownership_controller.dissolve')
    router.post('/:id/transfer', '#controllers/co_ownership_controller.transfer')
  }).prefix('/co-ownership').use(middleware.auth())

  // Admin routes
  router.group(() => {
    router.get('/dashboard', '#controllers/admin_controller.dashboard')
    router.get('/users', '#controllers/admin_controller.getUsers')
    router.get('/loans', '#controllers/admin_controller.getLoans')
    router.get('/assets', '#controllers/admin_controller.getAssets')
    router.get('/payments', '#controllers/admin_controller.getPayments')
    router.get('/sales', '#controllers/admin_controller.getSales')
    router.post('/users/:id/verify-kyc', '#controllers/admin_controller.verifyKyc')
    router.post('/users/:id/suspend', '#controllers/admin_controller.suspendUser')
    router.post('/users/:id/activate', '#controllers/admin_controller.activateUser')
    router.get('/reports/loans', '#controllers/admin_controller.loanReports')
    router.get('/reports/payments', '#controllers/admin_controller.paymentReports')
    router.get('/reports/assets', '#controllers/admin_controller.assetReports')
  }).prefix('/admin').use([middleware.auth(), middleware.admin()])

  // Notification routes
  router.group(() => {
    router.get('/', '#controllers/notifications_controller.index')
    router.put('/:id/read', '#controllers/notifications_controller.markAsRead')
    router.put('/read-all', '#controllers/notifications_controller.markAllAsRead')
  }).prefix('/notifications').use(middleware.auth())

}).prefix('/api/v1')

// Webhook routes (no auth required)
router.group(() => {
  router.post('/mpesa/callback', '#controllers/webhooks_controller.mpesaCallback')
  router.post('/airtel/callback', '#controllers/webhooks_controller.airtelCallback')
  router.post('/kyc/callback', '#controllers/webhooks_controller.kycCallback')
}).prefix('/webhooks')
