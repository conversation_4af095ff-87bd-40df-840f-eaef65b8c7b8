import type { HttpContext } from '@adonisjs/core/http'
import User from '#models/User'
import Profile from '#models/Profile'
import { DateTime } from 'luxon'

export default class UsersController {
  /**
   * Get current user profile
   */
  async me({ auth, response }: HttpContext) {
    try {
      const user = await User.query()
        .where('id', auth.user!.id)
        .preload('profile')
        .preload('assets')
        .preload('borrowedLoans', (query) => {
          query.preload('asset').preload('lender', (lenderQuery) => {
            lenderQuery.preload('profile')
          })
        })
        .preload('lentLoans', (query) => {
          query.preload('asset').preload('borrower', (borrowerQuery) => {
            borrowerQuery.preload('profile')
          })
        })
        .first()

      if (!user) {
        return response.status(404).json({
          success: false,
          message: 'User not found',
        })
      }

      return response.json({
        success: true,
        data: { user },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch user profile',
        error: error.message,
      })
    }
  }

  /**
   * Update user profile
   */
  async updateProfile({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const data = request.all()

      // Update user fields
      const userFields = ['email', 'phone']
      const userUpdates: any = {}
      
      for (const field of userFields) {
        if (data[field] !== undefined) {
          userUpdates[field] = data[field]
        }
      }

      if (Object.keys(userUpdates).length > 0) {
        await user.merge(userUpdates).save()
      }

      // Update or create profile
      let profile = await Profile.findBy('userId', user.id)
      
      if (!profile) {
        profile = new Profile()
        profile.userId = user.id
        profile.country = 'Kenya'
      }

      // Profile fields
      const profileFields = [
        'firstName', 'lastName', 'middleName', 'dateOfBirth', 'gender',
        'nationalId', 'passportNumber', 'kraPin', 'address', 'city', 'county',
        'postalCode', 'occupation', 'employer', 'monthlyIncome',
        'nextOfKinName', 'nextOfKinPhone', 'nextOfKinRelationship',
        'emergencyContactName', 'emergencyContactPhone', 'companyName',
        'companyRegistrationNumber', 'businessType', 'yearsInBusiness',
        'annualRevenue'
      ]

      const profileUpdates: any = {}
      for (const field of profileFields) {
        if (data[field] !== undefined) {
          profileUpdates[field] = data[field]
        }
      }

      if (data.dateOfBirth) {
        profileUpdates.dateOfBirth = DateTime.fromISO(data.dateOfBirth)
      }

      await profile.merge(profileUpdates).save()

      // Reload user with profile
      await user.load('profile')

      return response.json({
        success: true,
        message: 'Profile updated successfully',
        data: { user },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to update profile',
        error: error.message,
      })
    }
  }

  /**
   * Upload documents
   */
  async uploadDocuments({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const { documentType, fileUrl } = request.only(['documentType', 'fileUrl'])

      let profile = await Profile.findBy('userId', user.id)
      if (!profile) {
        return response.status(404).json({
          success: false,
          message: 'Profile not found',
        })
      }

      // Map document types to profile fields
      const documentMapping: Record<string, string> = {
        'profile_picture': 'profilePicture',
        'id_front': 'idDocumentFront',
        'id_back': 'idDocumentBack',
        'proof_of_income': 'proofOfIncome',
        'bank_statement': 'bankStatement',
        'business_registration': 'businessRegistration',
        'business_license': 'businessLicense',
      }

      const profileField = documentMapping[documentType]
      if (!profileField) {
        return response.status(400).json({
          success: false,
          message: 'Invalid document type',
        })
      }

      // Update profile with document URL
      profile[profileField] = fileUrl
      await profile.save()

      return response.json({
        success: true,
        message: 'Document uploaded successfully',
        data: { profile },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to upload document',
        error: error.message,
      })
    }
  }

  /**
   * Get KYC status
   */
  async kycStatus({ auth, response }: HttpContext) {
    try {
      const user = await User.query()
        .where('id', auth.user!.id)
        .preload('profile')
        .first()

      if (!user || !user.profile) {
        return response.status(404).json({
          success: false,
          message: 'User profile not found',
        })
      }

      const profile = user.profile
      let completionPercentage = 0
      let missingFields: string[] = []

      if (user.userType === 'borrower') {
        const requiredFields = [
          'firstName', 'lastName', 'dateOfBirth', 'nationalId',
          'address', 'city', 'county', 'occupation', 'monthlyIncome',
          'idDocumentFront', 'idDocumentBack'
        ]

        const completedFields = requiredFields.filter(field => profile[field])
        completionPercentage = (completedFields.length / requiredFields.length) * 100
        missingFields = requiredFields.filter(field => !profile[field])
      } else if (user.userType === 'lender') {
        const requiredFields = [
          'companyName', 'companyRegistrationNumber', 'businessType',
          'businessRegistration', 'businessLicense', 'address', 'city', 'county'
        ]

        const completedFields = requiredFields.filter(field => profile[field])
        completionPercentage = (completedFields.length / requiredFields.length) * 100
        missingFields = requiredFields.filter(field => !profile[field])
      }

      return response.json({
        success: true,
        data: {
          kycStatus: user.kycStatus,
          completionPercentage: Math.round(completionPercentage),
          missingFields,
          canSubmitKyc: completionPercentage === 100,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch KYC status',
        error: error.message,
      })
    }
  }

  /**
   * Submit KYC for verification
   */
  async submitKyc({ auth, response }: HttpContext) {
    try {
      const user = auth.user!
      
      // Check if profile is complete
      const kycStatusResponse = await this.kycStatus({ auth, response } as HttpContext)
      const kycData = JSON.parse(kycStatusResponse.getBody())

      if (!kycData.data.canSubmitKyc) {
        return response.status(400).json({
          success: false,
          message: 'Profile is incomplete. Please complete all required fields.',
          data: {
            missingFields: kycData.data.missingFields,
          },
        })
      }

      // Update KYC status to pending (if not already)
      if (user.kycStatus !== 'pending') {
        user.kycStatus = 'pending'
        await user.save()
      }

      // Here you would typically integrate with a KYC service
      // For now, we'll just mark it as submitted

      return response.json({
        success: true,
        message: 'KYC submitted for verification',
        data: {
          kycStatus: user.kycStatus,
          submittedAt: DateTime.now().toISO(),
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to submit KYC',
        error: error.message,
      })
    }
  }
}
