import type { HttpContext } from '@adonisjs/core/http'
import Payment from '#models/Payment'
import Loan from '#models/Loan'
import MpesaService from '#services/MpesaService'
import { DateTime } from 'luxon'

export default class PaymentsController {
  private mpesaService: MpesaService

  constructor() {
    this.mpesaService = new MpesaService()
  }

  /**
   * Get all payments for the authenticated user
   */
  async index({ auth, request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 10)
      const status = request.input('status')
      const paymentMethod = request.input('paymentMethod')
      const loanId = request.input('loanId')

      let query = Payment.query()

      // Filter by user's loans
      if (auth.user!.userType === 'borrower') {
        const userLoanIds = await Loan.query()
          .where('borrowerId', auth.user!.id)
          .select('id')
          .then(loans => loans.map(loan => loan.id))
        
        query = query.whereIn('loanId', userLoanIds)
      } else if (auth.user!.userType === 'lender') {
        const userLoanIds = await Loan.query()
          .where('lenderId', auth.user!.id)
          .select('id')
          .then(loans => loans.map(loan => loan.id))
        
        query = query.whereIn('loanId', userLoanIds)
      }

      if (status) {
        query = query.where('status', status)
      }

      if (paymentMethod) {
        query = query.where('paymentMethod', paymentMethod)
      }

      if (loanId) {
        query = query.where('loanId', loanId)
      }

      const payments = await query
        .preload('loan', (loanQuery) => {
          loanQuery.preload('asset').preload('borrower', (borrowerQuery) => {
            borrowerQuery.preload('profile')
          })
        })
        .preload('payer', (payerQuery) => {
          payerQuery.preload('profile')
        })
        .orderBy('createdAt', 'desc')
        .paginate(page, limit)

      return response.json({
        success: true,
        data: payments,
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch payments',
        error: error.message,
      })
    }
  }

  /**
   * Get a specific payment
   */
  async show({ auth, params, response }: HttpContext) {
    try {
      let query = Payment.query().where('id', params.id)

      // Ensure user can only see their own payments
      if (auth.user!.userType === 'borrower') {
        const userLoanIds = await Loan.query()
          .where('borrowerId', auth.user!.id)
          .select('id')
          .then(loans => loans.map(loan => loan.id))
        
        query = query.whereIn('loanId', userLoanIds)
      } else if (auth.user!.userType === 'lender') {
        const userLoanIds = await Loan.query()
          .where('lenderId', auth.user!.id)
          .select('id')
          .then(loans => loans.map(loan => loan.id))
        
        query = query.whereIn('loanId', userLoanIds)
      }

      const payment = await query
        .preload('loan', (loanQuery) => {
          loanQuery.preload('asset').preload('borrower', (borrowerQuery) => {
            borrowerQuery.preload('profile')
          }).preload('lender', (lenderQuery) => {
            lenderQuery.preload('profile')
          })
        })
        .preload('payer', (payerQuery) => {
          payerQuery.preload('profile')
        })
        .first()

      if (!payment) {
        return response.status(404).json({
          success: false,
          message: 'Payment not found',
        })
      }

      return response.json({
        success: true,
        data: { payment },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch payment',
        error: error.message,
      })
    }
  }

  /**
   * Initiate M-Pesa payment
   */
  async initiateMpesa({ auth, request, response }: HttpContext) {
    try {
      const { loanId, amount, phoneNumber, reference } = request.only([
        'loanId', 'amount', 'phoneNumber', 'reference'
      ])

      // Validate loan belongs to user
      const loan = await Loan.query()
        .where('id', loanId)
        .where('borrowerId', auth.user!.id)
        .where('status', 'active')
        .preload('asset')
        .first()

      if (!loan) {
        return response.status(404).json({
          success: false,
          message: 'Active loan not found',
        })
      }

      // Validate amount
      if (amount <= 0 || amount > loan.outstandingBalance) {
        return response.status(400).json({
          success: false,
          message: 'Invalid payment amount',
        })
      }

      // Validate phone number
      if (!this.mpesaService.isValidPhoneNumber(phoneNumber)) {
        return response.status(400).json({
          success: false,
          message: 'Invalid phone number format',
        })
      }

      // Calculate payment breakdown
      const { principalAmount, interestAmount } = this.calculatePaymentBreakdown(
        amount,
        loan.outstandingBalance,
        loan.interestRate
      )

      // Create payment record
      const payment = await Payment.create({
        loanId: loan.id,
        payerId: auth.user!.id,
        amount,
        principalAmount,
        interestAmount,
        penaltyAmount: 0,
        paymentMethod: 'mpesa',
        transactionId: Payment.generateTransactionId(),
        status: 'pending',
        paymentType: 'regular',
        reference,
      })

      // Initiate M-Pesa STK push
      const mpesaResponse = await this.mpesaService.initiateSTKPush({
        phoneNumber,
        amount,
        accountReference: payment.transactionId,
        transactionDesc: `HakiMali loan payment for ${loan.asset.name}`,
        callbackUrl: `${process.env.APP_URL}/webhooks/mpesa/callback`,
      })

      // Update payment with M-Pesa details
      payment.externalTransactionId = mpesaResponse.CheckoutRequestID
      await payment.save()

      return response.json({
        success: true,
        message: 'M-Pesa payment initiated successfully',
        data: {
          payment,
          mpesaResponse: {
            checkoutRequestId: mpesaResponse.CheckoutRequestID,
            customerMessage: mpesaResponse.CustomerMessage,
          },
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to initiate M-Pesa payment',
        error: error.message,
      })
    }
  }

  /**
   * Handle M-Pesa callback
   */
  async mpesaCallback({ request, response }: HttpContext) {
    try {
      const callbackData = request.body()

      // Process the callback
      const result = this.mpesaService.processCallback(callbackData)

      if (result.success) {
        // Find the payment by checkout request ID
        const payment = await Payment.query()
          .where('externalTransactionId', callbackData.CheckoutRequestID)
          .preload('loan')
          .first()

        if (payment) {
          // Update payment status
          payment.status = 'completed'
          payment.processedAt = DateTime.now()
          payment.externalTransactionId = result.receiptNumber || payment.externalTransactionId
          await payment.save()

          // Update loan balances
          const loan = payment.loan
          loan.principalPaid += payment.principalAmount
          loan.interestPaid += payment.interestAmount
          loan.outstandingBalance -= payment.amount
          loan.lastPaymentDate = DateTime.now()

          // Calculate next payment date
          if (loan.outstandingBalance > 0) {
            loan.nextPaymentDate = DateTime.now().plus({ months: 1 })
          } else {
            loan.status = 'completed'
            loan.completedAt = DateTime.now()
          }

          await loan.save()

          // Check if co-ownership should be activated
          if (!loan.isCoOwnershipActive && loan.qualifiesForCoOwnership) {
            // Activate co-ownership logic would go here
          }
        }
      } else {
        // Payment failed - update payment status
        const payment = await Payment.query()
          .where('externalTransactionId', callbackData.CheckoutRequestID)
          .first()

        if (payment) {
          payment.status = 'failed'
          payment.failureReason = result.error
          await payment.save()
        }
      }

      return response.json({
        success: true,
        message: 'Callback processed successfully',
      })
    } catch (error) {
      console.error('M-Pesa callback error:', error)
      return response.status(500).json({
        success: false,
        message: 'Failed to process callback',
        error: error.message,
      })
    }
  }

  /**
   * Initiate Airtel Money payment
   */
  async initiateAirtel({ auth, request, response }: HttpContext) {
    try {
      // Similar implementation to M-Pesa but for Airtel Money
      // This would integrate with Airtel Money API
      
      return response.json({
        success: false,
        message: 'Airtel Money integration coming soon',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to initiate Airtel Money payment',
        error: error.message,
      })
    }
  }

  /**
   * Handle Airtel Money callback
   */
  async airtelCallback({ request, response }: HttpContext) {
    try {
      // Handle Airtel Money callback
      return response.json({
        success: true,
        message: 'Airtel callback processed',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to process Airtel callback',
        error: error.message,
      })
    }
  }

  /**
   * Calculate payment breakdown (principal vs interest)
   */
  private calculatePaymentBreakdown(amount: number, outstandingBalance: number, annualRate: number) {
    const monthlyRate = annualRate / 100 / 12
    const interestAmount = Math.min(amount, outstandingBalance * monthlyRate)
    const principalAmount = amount - interestAmount

    return {
      principalAmount: Math.round(principalAmount * 100) / 100,
      interestAmount: Math.round(interestAmount * 100) / 100,
    }
  }
}
