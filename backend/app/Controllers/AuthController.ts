import type { HttpContext } from '@adonisjs/core/http'
import User from '#models/User'
import Profile from '#models/Profile'
import hash from '@adonisjs/core/services/hash'
import jwt from 'jsonwebtoken'
import env from '#start/env'
import { DateTime } from 'luxon'

export default class AuthController {
  /**
   * Register a new user
   */
  async register({ request, response }: HttpContext) {
    try {
      const data = request.only([
        'email',
        'phone',
        'password',
        'userType',
        'firstName',
        'lastName',
        'companyName',
      ])

      // Check if user already exists
      const existingUser = await User.query()
        .where('email', data.email)
        .orWhere('phone', data.phone)
        .first()

      if (existingUser) {
        return response.status(409).json({
          success: false,
          message: 'User with this email or phone already exists',
        })
      }

      // Create user
      const user = await User.create({
        email: data.email,
        phone: data.phone,
        password: data.password,
        userType: data.userType,
        isVerified: false,
        isActive: true,
        kycStatus: 'pending',
        twoFactorEnabled: false,
      })

      // Create profile
      const profileData: any = {
        userId: user.id,
        country: 'Kenya',
      }

      if (data.userType === 'borrower') {
        profileData.firstName = data.firstName
        profileData.lastName = data.lastName
      } else if (data.userType === 'lender') {
        profileData.companyName = data.companyName
      }

      await Profile.create(profileData)

      // Generate JWT token
      const token = this.generateToken(user)

      // Send verification email/SMS (implement later)
      // await this.sendVerificationEmail(user)
      // await this.sendVerificationSMS(user)

      return response.status(201).json({
        success: true,
        message: 'User registered successfully',
        data: {
          user: {
            id: user.id,
            email: user.email,
            phone: user.phone,
            userType: user.userType,
            isVerified: user.isVerified,
            kycStatus: user.kycStatus,
          },
          token,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Registration failed',
        error: error.message,
      })
    }
  }

  /**
   * Login user
   */
  async login({ request, response }: HttpContext) {
    try {
      const { email, phone, password } = request.only(['email', 'phone', 'password'])

      if (!email && !phone) {
        return response.status(400).json({
          success: false,
          message: 'Email or phone is required',
        })
      }

      // Find user
      const user = await User.query()
        .where((query) => {
          if (email) query.where('email', email)
          if (phone) query.where('phone', phone)
        })
        .preload('profile')
        .first()

      if (!user) {
        return response.status(401).json({
          success: false,
          message: 'Invalid credentials',
        })
      }

      // Verify password
      const isValidPassword = await hash.verify(user.password, password)
      if (!isValidPassword) {
        return response.status(401).json({
          success: false,
          message: 'Invalid credentials',
        })
      }

      // Check if user is active
      if (!user.isActive) {
        return response.status(403).json({
          success: false,
          message: 'Account is suspended',
        })
      }

      // Update last login
      user.lastLoginAt = DateTime.now()
      await user.save()

      // Generate token
      const token = this.generateToken(user)

      return response.json({
        success: true,
        message: 'Login successful',
        data: {
          user: {
            id: user.id,
            email: user.email,
            phone: user.phone,
            userType: user.userType,
            isVerified: user.isVerified,
            kycStatus: user.kycStatus,
            twoFactorEnabled: user.twoFactorEnabled,
            profile: user.profile,
          },
          token,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Login failed',
        error: error.message,
      })
    }
  }

  /**
   * Logout user
   */
  async logout({ response }: HttpContext) {
    // In a stateless JWT system, logout is handled client-side
    // Here we could implement token blacklisting if needed
    return response.json({
      success: true,
      message: 'Logged out successfully',
    })
  }

  /**
   * Refresh token
   */
  async refresh({ request, response }: HttpContext) {
    try {
      const { token } = request.only(['token'])

      if (!token) {
        return response.status(400).json({
          success: false,
          message: 'Token is required',
        })
      }

      // Verify and decode token
      const decoded = jwt.verify(token, env.get('JWT_SECRET')) as any
      const user = await User.find(decoded.userId)

      if (!user || !user.isActive) {
        return response.status(401).json({
          success: false,
          message: 'Invalid token',
        })
      }

      // Generate new token
      const newToken = this.generateToken(user)

      return response.json({
        success: true,
        message: 'Token refreshed successfully',
        data: { token: newToken },
      })
    } catch (error) {
      return response.status(401).json({
        success: false,
        message: 'Invalid token',
      })
    }
  }

  /**
   * Forgot password
   */
  async forgotPassword({ request, response }: HttpContext) {
    try {
      const { email } = request.only(['email'])

      const user = await User.findBy('email', email)
      if (!user) {
        // Don't reveal if email exists
        return response.json({
          success: true,
          message: 'If the email exists, a reset link has been sent',
        })
      }

      // Generate reset token and send email (implement later)
      // const resetToken = this.generateResetToken(user)
      // await this.sendPasswordResetEmail(user, resetToken)

      return response.json({
        success: true,
        message: 'If the email exists, a reset link has been sent',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to process password reset request',
      })
    }
  }

  /**
   * Reset password
   */
  async resetPassword({ request, response }: HttpContext) {
    try {
      const { token, password } = request.only(['token', 'password'])

      // Verify reset token and update password (implement later)
      // const user = await this.verifyResetToken(token)
      // user.password = password
      // await user.save()

      return response.json({
        success: true,
        message: 'Password reset successfully',
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Invalid or expired reset token',
      })
    }
  }

  /**
   * Verify email
   */
  async verifyEmail({ request, response }: HttpContext) {
    try {
      const { token } = request.only(['token'])

      // Verify email token and update user (implement later)
      // const user = await this.verifyEmailToken(token)
      // user.isVerified = true
      // user.emailVerifiedAt = DateTime.now()
      // await user.save()

      return response.json({
        success: true,
        message: 'Email verified successfully',
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Invalid or expired verification token',
      })
    }
  }

  /**
   * Resend verification email
   */
  async resendVerification({ auth, response }: HttpContext) {
    try {
      const user = auth.user!

      if (user.isVerified) {
        return response.status(400).json({
          success: false,
          message: 'Email is already verified',
        })
      }

      // Send verification email (implement later)
      // await this.sendVerificationEmail(user)

      return response.json({
        success: true,
        message: 'Verification email sent',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to send verification email',
      })
    }
  }

  /**
   * Verify phone
   */
  async verifyPhone({ request, response }: HttpContext) {
    try {
      const { phone, code } = request.only(['phone', 'code'])

      // Verify phone code (implement later)
      // const isValid = await this.verifyPhoneCode(phone, code)

      return response.json({
        success: true,
        message: 'Phone verified successfully',
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Invalid verification code',
      })
    }
  }

  /**
   * Enable 2FA
   */
  async enable2FA({ auth, response }: HttpContext) {
    try {
      const user = auth.user!

      // Generate 2FA secret and QR code (implement later)
      // const secret = this.generate2FASecret()
      // const qrCode = this.generate2FAQRCode(user, secret)

      return response.json({
        success: true,
        message: '2FA setup initiated',
        data: {
          // secret,
          // qrCode,
        },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to enable 2FA',
      })
    }
  }

  /**
   * Verify 2FA
   */
  async verify2FA({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const { code } = request.only(['code'])

      // Verify 2FA code (implement later)
      // const isValid = this.verify2FACode(user.twoFactorSecret, code)

      return response.json({
        success: true,
        message: '2FA verified successfully',
      })
    } catch (error) {
      return response.status(400).json({
        success: false,
        message: 'Invalid 2FA code',
      })
    }
  }

  /**
   * Disable 2FA
   */
  async disable2FA({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const { password } = request.only(['password'])

      // Verify password
      const isValidPassword = await hash.verify(user.password, password)
      if (!isValidPassword) {
        return response.status(401).json({
          success: false,
          message: 'Invalid password',
        })
      }

      // Disable 2FA
      user.twoFactorEnabled = false
      user.twoFactorSecret = null
      await user.save()

      return response.json({
        success: true,
        message: '2FA disabled successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to disable 2FA',
      })
    }
  }

  /**
   * Generate JWT token
   */
  private generateToken(user: User): string {
    return jwt.sign(
      {
        userId: user.id,
        email: user.email,
        userType: user.userType,
      },
      env.get('JWT_SECRET'),
      {
        expiresIn: env.get('JWT_EXPIRES_IN'),
      }
    )
  }
}
