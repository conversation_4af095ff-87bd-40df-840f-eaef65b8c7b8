import type { HttpContext } from '@adonisjs/core/http'
import Loan from '#models/Loan'
import Asset from '#models/Asset'
import Payment from '#models/Payment'
import CoOwnership from '#models/CoOwnership'
import { DateTime } from 'luxon'

export default class LoansController {
  /**
   * Get all loans for the authenticated user
   */
  async index({ auth, request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 10)
      const status = request.input('status')
      const userType = auth.user!.userType

      let query = Loan.query()

      // Filter based on user type
      if (userType === 'borrower') {
        query = query.where('borrowerId', auth.user!.id)
      } else if (userType === 'lender') {
        query = query.where('lenderId', auth.user!.id)
      } else {
        // Ad<PERSON> can see all loans
      }

      if (status) {
        query = query.where('status', status)
      }

      const loans = await query
        .preload('borrower', (borrowerQuery) => {
          borrowerQuery.preload('profile')
        })
        .preload('lender', (lenderQuery) => {
          lenderQuery.preload('profile')
        })
        .preload('asset')
        .preload('coOwnership')
        .orderBy('createdAt', 'desc')
        .paginate(page, limit)

      return response.json({
        success: true,
        data: loans,
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch loans',
        error: error.message,
      })
    }
  }

  /**
   * Create a new loan application
   */
  async store({ auth, request, response }: HttpContext) {
    try {
      const user = auth.user!

      // Only borrowers can create loan applications
      if (user.userType !== 'borrower') {
        return response.status(403).json({
          success: false,
          message: 'Only borrowers can create loan applications',
        })
      }

      const data = request.only([
        'assetId', 'lenderId', 'principalAmount', 'interestRate',
        'termMonths', 'purpose', 'coOwnershipThreshold', 'borrowerEquityPercentage'
      ])

      // Verify asset belongs to borrower
      const asset = await Asset.query()
        .where('id', data.assetId)
        .where('userId', user.id)
        .where('status', 'available')
        .first()

      if (!asset) {
        return response.status(400).json({
          success: false,
          message: 'Asset not found or not available for financing',
        })
      }

      // Calculate loan terms
      const monthlyInterestRate = data.interestRate / 100 / 12
      const monthlyPayment = this.calculateMonthlyPayment(
        data.principalAmount,
        monthlyInterestRate,
        data.termMonths
      )
      const totalAmount = monthlyPayment * data.termMonths

      // Generate loan number
      const loanNumber = Loan.generateLoanNumber()

      // Create loan
      const loan = await Loan.create({
        loanNumber,
        borrowerId: user.id,
        lenderId: data.lenderId,
        assetId: data.assetId,
        principalAmount: data.principalAmount,
        interestRate: data.interestRate,
        termMonths: data.termMonths,
        monthlyPayment,
        totalAmount,
        outstandingBalance: totalAmount,
        principalPaid: 0,
        interestPaid: 0,
        status: 'pending',
        purpose: data.purpose,
        coOwnershipThreshold: data.coOwnershipThreshold || 50,
        borrowerEquityPercentage: data.borrowerEquityPercentage || 50,
        isCoOwnershipActive: false,
      })

      await loan.load('borrower', (borrowerQuery) => {
        borrowerQuery.preload('profile')
      })
      await loan.load('lender', (lenderQuery) => {
        lenderQuery.preload('profile')
      })
      await loan.load('asset')

      return response.status(201).json({
        success: true,
        message: 'Loan application created successfully',
        data: { loan },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to create loan application',
        error: error.message,
      })
    }
  }

  /**
   * Get a specific loan
   */
  async show({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user!
      let query = Loan.query().where('id', params.id)

      // Filter based on user type
      if (user.userType === 'borrower') {
        query = query.where('borrowerId', user.id)
      } else if (user.userType === 'lender') {
        query = query.where('lenderId', user.id)
      }

      const loan = await query
        .preload('borrower', (borrowerQuery) => {
          borrowerQuery.preload('profile')
        })
        .preload('lender', (lenderQuery) => {
          lenderQuery.preload('profile')
        })
        .preload('asset')
        .preload('payments', (paymentQuery) => {
          paymentQuery.orderBy('createdAt', 'desc')
        })
        .preload('coOwnership')
        .first()

      if (!loan) {
        return response.status(404).json({
          success: false,
          message: 'Loan not found',
        })
      }

      return response.json({
        success: true,
        data: { loan },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch loan',
        error: error.message,
      })
    }
  }

  /**
   * Update a loan (limited fields)
   */
  async update({ auth, params, request, response }: HttpContext) {
    try {
      const user = auth.user!
      let query = Loan.query().where('id', params.id)

      // Only lenders can update loans
      if (user.userType !== 'lender') {
        return response.status(403).json({
          success: false,
          message: 'Only lenders can update loans',
        })
      }

      query = query.where('lenderId', user.id)

      const loan = await query.first()

      if (!loan) {
        return response.status(404).json({
          success: false,
          message: 'Loan not found',
        })
      }

      // Only allow updates for pending loans
      if (loan.status !== 'pending') {
        return response.status(400).json({
          success: false,
          message: 'Can only update pending loans',
        })
      }

      const data = request.only([
        'interestRate', 'termMonths', 'coOwnershipThreshold', 'borrowerEquityPercentage'
      ])

      // Recalculate if interest rate or term changed
      if (data.interestRate || data.termMonths) {
        const interestRate = data.interestRate || loan.interestRate
        const termMonths = data.termMonths || loan.termMonths
        const monthlyInterestRate = interestRate / 100 / 12
        
        const monthlyPayment = this.calculateMonthlyPayment(
          loan.principalAmount,
          monthlyInterestRate,
          termMonths
        )
        const totalAmount = monthlyPayment * termMonths

        data.monthlyPayment = monthlyPayment
        data.totalAmount = totalAmount
        data.outstandingBalance = totalAmount
      }

      await loan.merge(data).save()

      return response.json({
        success: true,
        message: 'Loan updated successfully',
        data: { loan },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to update loan',
        error: error.message,
      })
    }
  }

  /**
   * Approve a loan
   */
  async approve({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user!

      // Only lenders can approve loans
      if (user.userType !== 'lender') {
        return response.status(403).json({
          success: false,
          message: 'Only lenders can approve loans',
        })
      }

      const loan = await Loan.query()
        .where('id', params.id)
        .where('lenderId', user.id)
        .where('status', 'pending')
        .first()

      if (!loan) {
        return response.status(404).json({
          success: false,
          message: 'Loan not found or not pending',
        })
      }

      loan.status = 'approved'
      loan.approvedAt = DateTime.now()
      await loan.save()

      return response.json({
        success: true,
        message: 'Loan approved successfully',
        data: { loan },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to approve loan',
        error: error.message,
      })
    }
  }

  /**
   * Reject a loan
   */
  async reject({ auth, params, request, response }: HttpContext) {
    try {
      const user = auth.user!

      // Only lenders can reject loans
      if (user.userType !== 'lender') {
        return response.status(403).json({
          success: false,
          message: 'Only lenders can reject loans',
        })
      }

      const loan = await Loan.query()
        .where('id', params.id)
        .where('lenderId', user.id)
        .where('status', 'pending')
        .first()

      if (!loan) {
        return response.status(404).json({
          success: false,
          message: 'Loan not found or not pending',
        })
      }

      const { reason } = request.only(['reason'])

      loan.status = 'rejected'
      // You might want to add a rejection reason field to the loan model
      await loan.save()

      return response.json({
        success: true,
        message: 'Loan rejected successfully',
        data: { loan, rejectionReason: reason },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to reject loan',
        error: error.message,
      })
    }
  }

  /**
   * Disburse a loan
   */
  async disburse({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user!

      // Only lenders can disburse loans
      if (user.userType !== 'lender') {
        return response.status(403).json({
          success: false,
          message: 'Only lenders can disburse loans',
        })
      }

      const loan = await Loan.query()
        .where('id', params.id)
        .where('lenderId', user.id)
        .where('status', 'approved')
        .preload('asset')
        .first()

      if (!loan) {
        return response.status(404).json({
          success: false,
          message: 'Loan not found or not approved',
        })
      }

      // Update loan status
      loan.status = 'active'
      loan.disbursedAt = DateTime.now()
      loan.nextPaymentDate = DateTime.now().plus({ months: 1 })
      await loan.save()

      // Update asset status
      const asset = loan.asset
      asset.status = 'financed'
      await asset.save()

      return response.json({
        success: true,
        message: 'Loan disbursed successfully',
        data: { loan },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to disburse loan',
        error: error.message,
      })
    }
  }

  /**
   * Get payments for a loan
   */
  async getPayments({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user!
      let query = Loan.query().where('id', params.id)

      // Filter based on user type
      if (user.userType === 'borrower') {
        query = query.where('borrowerId', user.id)
      } else if (user.userType === 'lender') {
        query = query.where('lenderId', user.id)
      }

      const loan = await query.first()

      if (!loan) {
        return response.status(404).json({
          success: false,
          message: 'Loan not found',
        })
      }

      const payments = await Payment.query()
        .where('loanId', loan.id)
        .preload('payer', (payerQuery) => {
          payerQuery.preload('profile')
        })
        .orderBy('createdAt', 'desc')

      return response.json({
        success: true,
        data: { payments },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch payments',
        error: error.message,
      })
    }
  }

  /**
   * Make a payment
   */
  async makePayment({ auth, params, request, response }: HttpContext) {
    try {
      const user = auth.user!
      const { amount, paymentMethod } = request.only(['amount', 'paymentMethod'])

      const loan = await Loan.query()
        .where('id', params.id)
        .where('borrowerId', user.id)
        .where('status', 'active')
        .first()

      if (!loan) {
        return response.status(404).json({
          success: false,
          message: 'Active loan not found',
        })
      }

      // Calculate payment breakdown
      const { principalAmount, interestAmount } = this.calculatePaymentBreakdown(
        amount,
        loan.outstandingBalance,
        loan.interestRate
      )

      // Create payment record
      const payment = await Payment.create({
        loanId: loan.id,
        payerId: user.id,
        amount,
        principalAmount,
        interestAmount,
        penaltyAmount: 0,
        paymentMethod,
        transactionId: Payment.generateTransactionId(),
        status: 'pending',
        paymentType: 'regular',
      })

      // Here you would integrate with payment gateway (M-Pesa, etc.)
      // For now, we'll mark it as completed
      payment.status = 'completed'
      payment.processedAt = DateTime.now()
      await payment.save()

      // Update loan balances
      loan.principalPaid += principalAmount
      loan.interestPaid += interestAmount
      loan.outstandingBalance -= amount
      loan.lastPaymentDate = DateTime.now()
      
      // Calculate next payment date
      if (loan.outstandingBalance > 0) {
        loan.nextPaymentDate = DateTime.now().plus({ months: 1 })
      } else {
        loan.status = 'completed'
        loan.completedAt = DateTime.now()
      }

      await loan.save()

      // Check if co-ownership should be activated
      if (!loan.isCoOwnershipActive && loan.qualifiesForCoOwnership) {
        await this.activateCoOwnership(loan)
      }

      return response.json({
        success: true,
        message: 'Payment processed successfully',
        data: { payment, loan },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to process payment',
        error: error.message,
      })
    }
  }

  /**
   * Get payment schedule for a loan
   */
  async getPaymentSchedule({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user!
      let query = Loan.query().where('id', params.id)

      // Filter based on user type
      if (user.userType === 'borrower') {
        query = query.where('borrowerId', user.id)
      } else if (user.userType === 'lender') {
        query = query.where('lenderId', user.id)
      }

      const loan = await query.first()

      if (!loan) {
        return response.status(404).json({
          success: false,
          message: 'Loan not found',
        })
      }

      const schedule = this.generatePaymentSchedule(loan)

      return response.json({
        success: true,
        data: { schedule },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to generate payment schedule',
        error: error.message,
      })
    }
  }

  /**
   * Mark loan as default
   */
  async markDefault({ auth, params, response }: HttpContext) {
    try {
      const user = auth.user!

      // Only lenders can mark loans as default
      if (user.userType !== 'lender') {
        return response.status(403).json({
          success: false,
          message: 'Only lenders can mark loans as default',
        })
      }

      const loan = await Loan.query()
        .where('id', params.id)
        .where('lenderId', user.id)
        .where('status', 'active')
        .first()

      if (!loan) {
        return response.status(404).json({
          success: false,
          message: 'Active loan not found',
        })
      }

      loan.status = 'defaulted'
      loan.defaultDate = DateTime.now()
      await loan.save()

      return response.json({
        success: true,
        message: 'Loan marked as default',
        data: { loan },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to mark loan as default',
        error: error.message,
      })
    }
  }

  /**
   * Calculate monthly payment using loan formula
   */
  private calculateMonthlyPayment(principal: number, monthlyRate: number, months: number): number {
    if (monthlyRate === 0) {
      return principal / months
    }
    
    const payment = principal * (monthlyRate * Math.pow(1 + monthlyRate, months)) / 
                   (Math.pow(1 + monthlyRate, months) - 1)
    
    return Math.round(payment * 100) / 100
  }

  /**
   * Calculate payment breakdown (principal vs interest)
   */
  private calculatePaymentBreakdown(amount: number, outstandingBalance: number, annualRate: number) {
    const monthlyRate = annualRate / 100 / 12
    const interestAmount = Math.min(amount, outstandingBalance * monthlyRate)
    const principalAmount = amount - interestAmount

    return {
      principalAmount: Math.round(principalAmount * 100) / 100,
      interestAmount: Math.round(interestAmount * 100) / 100,
    }
  }

  /**
   * Generate payment schedule
   */
  private generatePaymentSchedule(loan: Loan) {
    const schedule = []
    let remainingBalance = loan.principalAmount
    const monthlyRate = loan.interestRate / 100 / 12

    for (let month = 1; month <= loan.termMonths; month++) {
      const interestPayment = remainingBalance * monthlyRate
      const principalPayment = loan.monthlyPayment - interestPayment
      remainingBalance -= principalPayment

      schedule.push({
        month,
        paymentAmount: loan.monthlyPayment,
        principalPayment: Math.round(principalPayment * 100) / 100,
        interestPayment: Math.round(interestPayment * 100) / 100,
        remainingBalance: Math.max(0, Math.round(remainingBalance * 100) / 100),
        dueDate: loan.disbursedAt ? 
          loan.disbursedAt.plus({ months: month }).toISODate() : 
          DateTime.now().plus({ months: month }).toISODate(),
      })
    }

    return schedule
  }

  /**
   * Activate co-ownership for a loan
   */
  private async activateCoOwnership(loan: Loan) {
    const asset = await Asset.find(loan.assetId)
    if (!asset) return

    const coOwnership = await CoOwnership.create({
      loanId: loan.id,
      assetId: loan.assetId,
      borrowerId: loan.borrowerId,
      lenderId: loan.lenderId,
      borrowerEquityPercentage: loan.borrowerEquityPercentage,
      lenderEquityPercentage: 100 - loan.borrowerEquityPercentage,
      activatedAt: DateTime.now(),
      principalPaidAtActivation: loan.principalPaid,
      assetValueAtActivation: asset.currentValue,
      agreementTerms: CoOwnership.generateAgreementTerms(
        loan.borrowerEquityPercentage,
        100 - loan.borrowerEquityPercentage,
        asset.currentValue,
        loan.principalPaid
      ),
      status: 'active',
    })

    loan.isCoOwnershipActive = true
    await loan.save()

    return coOwnership
  }
}
