import type { HttpContext } from '@adonisjs/core/http'
import Asset from '#models/Asset'
import AssetValuation from '#models/AssetValuation'
import { DateTime } from 'luxon'

export default class AssetsController {
  /**
   * Get all assets for the authenticated user
   */
  async index({ auth, request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 10)
      const type = request.input('type')
      const status = request.input('status')

      const query = Asset.query()
        .where('userId', auth.user!.id)
        .preload('valuations', (valuationQuery) => {
          valuationQuery.orderBy('createdAt', 'desc').limit(1)
        })

      if (type) {
        query.where('type', type)
      }

      if (status) {
        query.where('status', status)
      }

      const assets = await query
        .orderBy('createdAt', 'desc')
        .paginate(page, limit)

      return response.json({
        success: true,
        data: assets,
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch assets',
        error: error.message,
      })
    }
  }

  /**
   * Create a new asset
   */
  async store({ auth, request, response }: HttpContext) {
    try {
      const data = request.only([
        'type', 'category', 'name', 'description', 'brand', 'model', 'year',
        'serialNumber', 'registrationNumber', 'logbookNumber', 'titleDeedNumber',
        'location', 'size', 'condition', 'purchasePrice', 'currentValue',
        'images', 'documents', 'specifications'
      ])

      const asset = await Asset.create({
        ...data,
        userId: auth.user!.id,
        status: 'available',
        isVerified: false,
      })

      // Create initial valuation
      await AssetValuation.create({
        assetId: asset.id,
        valuationType: 'automated',
        currentValue: data.currentValue,
        valuationMethod: 'user_provided',
        notes: 'Initial valuation provided by user',
        isVerified: false,
      })

      // Update asset's last valuation date
      asset.lastValuationDate = DateTime.now()
      await asset.save()

      await asset.load('valuations')

      return response.status(201).json({
        success: true,
        message: 'Asset created successfully',
        data: { asset },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to create asset',
        error: error.message,
      })
    }
  }

  /**
   * Get a specific asset
   */
  async show({ auth, params, response }: HttpContext) {
    try {
      const asset = await Asset.query()
        .where('id', params.id)
        .where('userId', auth.user!.id)
        .preload('owner', (ownerQuery) => {
          ownerQuery.preload('profile')
        })
        .preload('valuations', (valuationQuery) => {
          valuationQuery.orderBy('createdAt', 'desc')
        })
        .preload('loans', (loanQuery) => {
          loanQuery.preload('borrower', (borrowerQuery) => {
            borrowerQuery.preload('profile')
          }).preload('lender', (lenderQuery) => {
            lenderQuery.preload('profile')
          })
        })
        .first()

      if (!asset) {
        return response.status(404).json({
          success: false,
          message: 'Asset not found',
        })
      }

      return response.json({
        success: true,
        data: { asset },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch asset',
        error: error.message,
      })
    }
  }

  /**
   * Update an asset
   */
  async update({ auth, params, request, response }: HttpContext) {
    try {
      const asset = await Asset.query()
        .where('id', params.id)
        .where('userId', auth.user!.id)
        .first()

      if (!asset) {
        return response.status(404).json({
          success: false,
          message: 'Asset not found',
        })
      }

      // Check if asset can be updated (not financed)
      if (asset.status === 'financed') {
        return response.status(400).json({
          success: false,
          message: 'Cannot update financed asset',
        })
      }

      const data = request.only([
        'category', 'name', 'description', 'brand', 'model', 'year',
        'serialNumber', 'registrationNumber', 'logbookNumber', 'titleDeedNumber',
        'location', 'size', 'condition', 'purchasePrice', 'currentValue',
        'images', 'documents', 'specifications'
      ])

      await asset.merge(data).save()

      // If current value changed, create new valuation
      if (data.currentValue && data.currentValue !== asset.currentValue) {
        await AssetValuation.create({
          assetId: asset.id,
          valuationType: 'automated',
          currentValue: data.currentValue,
          previousValue: asset.currentValue,
          valuationMethod: 'user_updated',
          notes: 'Value updated by user',
          isVerified: false,
        })

        asset.lastValuationDate = DateTime.now()
        await asset.save()
      }

      await asset.load('valuations')

      return response.json({
        success: true,
        message: 'Asset updated successfully',
        data: { asset },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to update asset',
        error: error.message,
      })
    }
  }

  /**
   * Delete an asset
   */
  async destroy({ auth, params, response }: HttpContext) {
    try {
      const asset = await Asset.query()
        .where('id', params.id)
        .where('userId', auth.user!.id)
        .first()

      if (!asset) {
        return response.status(404).json({
          success: false,
          message: 'Asset not found',
        })
      }

      // Check if asset can be deleted (not financed)
      if (asset.status === 'financed') {
        return response.status(400).json({
          success: false,
          message: 'Cannot delete financed asset',
        })
      }

      await asset.delete()

      return response.json({
        success: true,
        message: 'Asset deleted successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to delete asset',
        error: error.message,
      })
    }
  }

  /**
   * Add a new valuation to an asset
   */
  async addValuation({ auth, params, request, response }: HttpContext) {
    try {
      const asset = await Asset.query()
        .where('id', params.id)
        .where('userId', auth.user!.id)
        .first()

      if (!asset) {
        return response.status(404).json({
          success: false,
          message: 'Asset not found',
        })
      }

      const data = request.only([
        'valuationType', 'currentValue', 'valuationMethod', 'notes',
        'supportingDocuments', 'marketConditions'
      ])

      const valuation = await AssetValuation.create({
        ...data,
        assetId: asset.id,
        valuerId: auth.user!.id,
        previousValue: asset.currentValue,
        isVerified: false,
      })

      // Update asset's current value and last valuation date
      asset.currentValue = data.currentValue
      asset.lastValuationDate = DateTime.now()
      await asset.save()

      return response.status(201).json({
        success: true,
        message: 'Valuation added successfully',
        data: { valuation },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to add valuation',
        error: error.message,
      })
    }
  }

  /**
   * Get valuations for an asset
   */
  async getValuations({ auth, params, response }: HttpContext) {
    try {
      const asset = await Asset.query()
        .where('id', params.id)
        .where('userId', auth.user!.id)
        .first()

      if (!asset) {
        return response.status(404).json({
          success: false,
          message: 'Asset not found',
        })
      }

      const valuations = await AssetValuation.query()
        .where('assetId', asset.id)
        .preload('valuer', (valuerQuery) => {
          valuerQuery.preload('profile')
        })
        .orderBy('createdAt', 'desc')

      return response.json({
        success: true,
        data: { valuations },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to fetch valuations',
        error: error.message,
      })
    }
  }

  /**
   * Upload images for an asset
   */
  async uploadImages({ auth, params, request, response }: HttpContext) {
    try {
      const asset = await Asset.query()
        .where('id', params.id)
        .where('userId', auth.user!.id)
        .first()

      if (!asset) {
        return response.status(404).json({
          success: false,
          message: 'Asset not found',
        })
      }

      const { imageUrls } = request.only(['imageUrls'])

      if (!Array.isArray(imageUrls)) {
        return response.status(400).json({
          success: false,
          message: 'imageUrls must be an array',
        })
      }

      // Merge with existing images
      const existingImages = asset.images || []
      const updatedImages = [...existingImages, ...imageUrls]

      asset.images = updatedImages
      await asset.save()

      return response.json({
        success: true,
        message: 'Images uploaded successfully',
        data: { asset },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to upload images',
        error: error.message,
      })
    }
  }

  /**
   * Upload documents for an asset
   */
  async uploadDocuments({ auth, params, request, response }: HttpContext) {
    try {
      const asset = await Asset.query()
        .where('id', params.id)
        .where('userId', auth.user!.id)
        .first()

      if (!asset) {
        return response.status(404).json({
          success: false,
          message: 'Asset not found',
        })
      }

      const { documentUrls } = request.only(['documentUrls'])

      if (!Array.isArray(documentUrls)) {
        return response.status(400).json({
          success: false,
          message: 'documentUrls must be an array',
        })
      }

      // Merge with existing documents
      const existingDocuments = asset.documents || []
      const updatedDocuments = [...existingDocuments, ...documentUrls]

      asset.documents = updatedDocuments
      await asset.save()

      return response.json({
        success: true,
        message: 'Documents uploaded successfully',
        data: { asset },
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to upload documents',
        error: error.message,
      })
    }
  }
}
