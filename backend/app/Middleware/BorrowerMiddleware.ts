import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'

export default class BorrowerMiddleware {
  async handle({ auth, response }: HttpContext, next: NextFn) {
    const user = auth.user

    if (!user || user.userType !== 'borrower') {
      return response.status(403).json({
        success: false,
        message: 'Borrower access required',
      })
    }

    return next()
  }
}
