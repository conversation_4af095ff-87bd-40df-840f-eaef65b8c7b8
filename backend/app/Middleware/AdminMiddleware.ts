import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'

export default class AdminMiddleware {
  async handle({ auth, response }: HttpContext, next: NextFn) {
    const user = auth.user

    if (!user || user.userType !== 'admin') {
      return response.status(403).json({
        success: false,
        message: 'Admin access required',
      })
    }

    return next()
  }
}
