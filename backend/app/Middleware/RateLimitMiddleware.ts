import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import redis from '@adonisjs/redis/services/main'
import env from '#start/env'

export default class RateLimitMiddleware {
  async handle({ request, response }: HttpContext, next: NextFn) {
    const ip = request.ip()
    const key = `rate_limit:${ip}`
    const maxRequests = env.get('RATE_LIMIT_REQUESTS', 100)
    const windowMs = env.get('RATE_LIMIT_DURATION', 900) // 15 minutes in seconds

    try {
      // Get current count
      const current = await redis.get(key)
      const count = current ? parseInt(current) : 0

      if (count >= maxRequests) {
        return response.status(429).json({
          success: false,
          message: 'Too many requests, please try again later',
          retryAfter: windowMs,
        })
      }

      // Increment counter
      if (count === 0) {
        await redis.setex(key, windowMs, 1)
      } else {
        await redis.incr(key)
      }

      // Add rate limit headers
      response.header('X-RateLimit-Limit', maxRequests.toString())
      response.header('X-RateLimit-Remaining', (maxRequests - count - 1).toString())
      response.header('X-RateLimit-Reset', (Date.now() + windowMs * 1000).toString())

      return next()
    } catch (error) {
      // If Redis is down, allow the request to proceed
      console.error('Rate limiting error:', error)
      return next()
    }
  }
}
