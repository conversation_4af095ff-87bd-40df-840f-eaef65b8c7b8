import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'

/**
 * Container bindings middleware is used to bind values to the IoC
 * container for a given HTTP request.
 */
export default class ContainerBindingsMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    /**
     * Bind the current HTTP context to the IoC container. This
     * allows other parts of the application to access the
     * HTTP context using the container.
     */
    ctx.containerResolver.bindValue('HttpContext', ctx)

    return next()
  }
}
