import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'

export default class KycMiddleware {
  async handle({ auth, response }: HttpContext, next: NextFn) {
    const user = auth.user

    if (!user || user.kycStatus !== 'verified') {
      return response.status(403).json({
        success: false,
        message: 'KYC verification required',
        data: {
          kycStatus: user?.kycStatus || 'pending',
        },
      })
    }

    return next()
  }
}
