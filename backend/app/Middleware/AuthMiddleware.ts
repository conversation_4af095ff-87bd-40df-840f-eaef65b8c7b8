import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import jwt from 'jsonwebtoken'
import env from '#start/env'
import User from '#models/user'

export default class AuthMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    const { request, response } = ctx

    // Skip auth for certain routes
    const publicRoutes = ['/api/v1/auth/login', '/api/v1/auth/register', '/health', '/webhooks']
    const isPublicRoute = publicRoutes.some((route) => request.url().startsWith(route))

    if (isPublicRoute) {
      return next()
    }

    try {
      // Get token from header
      const authHeader = request.header('authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return response.status(401).json({
          success: false,
          message: 'Access token is required',
        })
      }

      const token = authHeader.substring(7) // Remove 'Bearer ' prefix

      // Verify token
      const decoded = jwt.verify(token, env.get('JWT_SECRET')) as any

      // Get user
      const user = await User.query()
        .where('id', decoded.userId)
        .preload('profile')
        .first()

      if (!user) {
        return response.status(401).json({
          success: false,
          message: 'Invalid token',
        })
      }

      // Check if user is active
      if (!user.isActive) {
        return response.status(403).json({
          success: false,
          message: 'Account is suspended',
        })
      }

      // Attach user to context
      ctx.auth = { user }

      return next()
    } catch (error) {
      return response.status(401).json({
        success: false,
        message: 'Invalid or expired token',
      })
    }
  }
}
