import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import AuditLog from '#models/audit_log'
import env from '#start/env'

export default class AuditMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    const { request, auth } = ctx

    // Skip audit for certain routes
    const skipRoutes = ['/health', '/api/v1/auth/refresh']
    const shouldSkip = skipRoutes.some((route) => request.url().startsWith(route))

    if (shouldSkip || !env.get('AUDIT_LOG_ENABLED')) {
      return next()
    }

    const startTime = Date.now()
    let statusCode = 200
    let error: any = null

    try {
      await next()
      statusCode = ctx.response.getStatus()
    } catch (err) {
      error = err
      statusCode = err.status || 500
      throw err
    } finally {
      // Log the request
      try {
        await AuditLog.create({
          userId: auth.user?.id || null,
          action: `${request.method()} ${request.url()}`,
          resource: this.extractResource(request.url()),
          resourceId: this.extractResourceId(request.url()),
          ipAddress: request.ip(),
          userAgent: request.header('user-agent'),
          requestData: this.sanitizeRequestData(request.all()),
          responseStatus: statusCode,
          duration: Date.now() - startTime,
          error: error ? error.message : null,
        })
      } catch (auditError) {
        console.error('Audit logging failed:', auditError)
      }
    }
  }

  private extractResource(url: string): string {
    const parts = url.split('/')
    const apiIndex = parts.indexOf('api')
    if (apiIndex !== -1 && parts.length > apiIndex + 2) {
      return parts[apiIndex + 2] // e.g., 'users', 'loans', 'assets'
    }
    return 'unknown'
  }

  private extractResourceId(url: string): number | null {
    const parts = url.split('/')
    for (const part of parts) {
      if (/^\d+$/.test(part)) {
        return parseInt(part)
      }
    }
    return null
  }

  private sanitizeRequestData(data: any): any {
    const sensitiveFields = ['password', 'token', 'secret', 'key']
    const sanitized = { ...data }

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]'
      }
    }

    return sanitized
  }
}
