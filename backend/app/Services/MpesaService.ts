import axios from 'axios'
import env from '#start/env'
import { DateTime } from 'luxon'

interface MpesaCredentials {
  consumerKey: string
  consumerSecret: string
  shortcode: string
  passkey: string
  environment: 'sandbox' | 'production'
}

interface StkPushRequest {
  phoneNumber: string
  amount: number
  accountReference: string
  transactionDesc: string
  callbackUrl: string
}

interface StkPushResponse {
  MerchantRequestID: string
  CheckoutRequestID: string
  ResponseCode: string
  ResponseDescription: string
  CustomerMessage: string
}

interface MpesaCallback {
  MerchantRequestID: string
  CheckoutRequestID: string
  ResultCode: number
  ResultDesc: string
  CallbackMetadata?: {
    Item: Array<{
      Name: string
      Value: string | number
    }>
  }
}

export default class MpesaService {
  private credentials: MpesaCredentials
  private baseUrl: string

  constructor() {
    this.credentials = {
      consumerKey: env.get('MPESA_CONSUMER_KEY'),
      consumerSecret: env.get('MPESA_CONSUMER_SECRET'),
      shortcode: env.get('MPESA_SHORTCODE'),
      passkey: env.get('MPESA_PASSKEY'),
      environment: env.get('MPESA_ENVIRONMENT'),
    }

    this.baseUrl = this.credentials.environment === 'production'
      ? 'https://api.safaricom.co.ke'
      : 'https://sandbox.safaricom.co.ke'
  }

  /**
   * Generate OAuth access token
   */
  private async getAccessToken(): Promise<string> {
    const auth = Buffer.from(
      `${this.credentials.consumerKey}:${this.credentials.consumerSecret}`
    ).toString('base64')

    try {
      const response = await axios.get(
        `${this.baseUrl}/oauth/v1/generate?grant_type=client_credentials`,
        {
          headers: {
            Authorization: `Basic ${auth}`,
          },
        }
      )

      return response.data.access_token
    } catch (error) {
      console.error('M-Pesa OAuth error:', error)
      throw new Error('Failed to get M-Pesa access token')
    }
  }

  /**
   * Generate password for STK push
   */
  private generatePassword(): string {
    const timestamp = DateTime.now().toFormat('yyyyMMddHHmmss')
    const password = Buffer.from(
      `${this.credentials.shortcode}${this.credentials.passkey}${timestamp}`
    ).toString('base64')

    return password
  }

  /**
   * Get current timestamp
   */
  private getTimestamp(): string {
    return DateTime.now().toFormat('yyyyMMddHHmmss')
  }

  /**
   * Format phone number to M-Pesa format
   */
  private formatPhoneNumber(phoneNumber: string): string {
    // Remove any non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, '')
    
    // Handle different formats
    if (cleaned.startsWith('254')) {
      return cleaned
    } else if (cleaned.startsWith('0')) {
      return '254' + cleaned.substring(1)
    } else if (cleaned.startsWith('7') || cleaned.startsWith('1')) {
      return '254' + cleaned
    }
    
    return cleaned
  }

  /**
   * Initiate STK Push payment
   */
  async initiateSTKPush(request: StkPushRequest): Promise<StkPushResponse> {
    try {
      const accessToken = await this.getAccessToken()
      const password = this.generatePassword()
      const timestamp = this.getTimestamp()
      const formattedPhone = this.formatPhoneNumber(request.phoneNumber)

      const payload = {
        BusinessShortCode: this.credentials.shortcode,
        Password: password,
        Timestamp: timestamp,
        TransactionType: 'CustomerPayBillOnline',
        Amount: Math.round(request.amount),
        PartyA: formattedPhone,
        PartyB: this.credentials.shortcode,
        PhoneNumber: formattedPhone,
        CallBackURL: request.callbackUrl,
        AccountReference: request.accountReference,
        TransactionDesc: request.transactionDesc,
      }

      const response = await axios.post(
        `${this.baseUrl}/mpesa/stkpush/v1/processrequest`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      )

      return response.data
    } catch (error: any) {
      console.error('M-Pesa STK Push error:', error.response?.data || error.message)
      throw new Error('Failed to initiate M-Pesa payment')
    }
  }

  /**
   * Query STK Push transaction status
   */
  async querySTKPushStatus(checkoutRequestId: string): Promise<any> {
    try {
      const accessToken = await this.getAccessToken()
      const password = this.generatePassword()
      const timestamp = this.getTimestamp()

      const payload = {
        BusinessShortCode: this.credentials.shortcode,
        Password: password,
        Timestamp: timestamp,
        CheckoutRequestID: checkoutRequestId,
      }

      const response = await axios.post(
        `${this.baseUrl}/mpesa/stkpushquery/v1/query`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      )

      return response.data
    } catch (error: any) {
      console.error('M-Pesa query error:', error.response?.data || error.message)
      throw new Error('Failed to query M-Pesa transaction status')
    }
  }

  /**
   * Process M-Pesa callback
   */
  processCallback(callbackData: MpesaCallback): {
    success: boolean
    transactionId?: string
    amount?: number
    phoneNumber?: string
    receiptNumber?: string
    transactionDate?: string
    error?: string
  } {
    try {
      if (callbackData.ResultCode === 0) {
        // Payment successful
        const metadata = callbackData.CallbackMetadata?.Item || []
        
        const getMetadataValue = (name: string) => {
          const item = metadata.find(item => item.Name === name)
          return item?.Value
        }

        return {
          success: true,
          transactionId: callbackData.CheckoutRequestID,
          amount: Number(getMetadataValue('Amount')),
          phoneNumber: String(getMetadataValue('PhoneNumber')),
          receiptNumber: String(getMetadataValue('MpesaReceiptNumber')),
          transactionDate: String(getMetadataValue('TransactionDate')),
        }
      } else {
        // Payment failed
        return {
          success: false,
          error: callbackData.ResultDesc,
        }
      }
    } catch (error) {
      console.error('M-Pesa callback processing error:', error)
      return {
        success: false,
        error: 'Failed to process callback',
      }
    }
  }

  /**
   * Validate callback authenticity (implement based on M-Pesa security requirements)
   */
  validateCallback(callbackData: any, signature?: string): boolean {
    // Implement signature validation if required by M-Pesa
    // This is a placeholder implementation
    return true
  }

  /**
   * Generate transaction reference
   */
  generateTransactionReference(prefix: string = 'HM'): string {
    const timestamp = DateTime.now().toFormat('yyyyMMddHHmmss')
    const random = Math.random().toString(36).substring(2, 8).toUpperCase()
    return `${prefix}${timestamp}${random}`
  }

  /**
   * Check if phone number is valid for M-Pesa
   */
  isValidPhoneNumber(phoneNumber: string): boolean {
    const formatted = this.formatPhoneNumber(phoneNumber)
    // Kenyan mobile numbers: 254 7XX XXX XXX or 254 1XX XXX XXX
    return /^254[17]\d{8}$/.test(formatted)
  }

  /**
   * Get transaction fee (if applicable)
   */
  getTransactionFee(amount: number): number {
    // Implement M-Pesa transaction fee calculation
    // This is a placeholder - actual fees depend on M-Pesa pricing
    if (amount <= 100) return 0
    if (amount <= 500) return 1
    if (amount <= 1000) return 5
    if (amount <= 1500) return 10
    if (amount <= 2500) return 15
    if (amount <= 3500) return 20
    if (amount <= 5000) return 25
    if (amount <= 7500) return 30
    if (amount <= 10000) return 35
    if (amount <= 15000) return 40
    if (amount <= 20000) return 45
    if (amount <= 35000) return 50
    if (amount <= 50000) return 55
    return Math.min(Math.floor(amount * 0.001), 100) // Max fee cap
  }
}
