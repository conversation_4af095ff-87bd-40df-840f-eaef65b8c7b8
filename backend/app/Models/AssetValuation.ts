import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Asset from './Asset.js'
import User from './User.js'

export default class AssetValuation extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare assetId: number

  @column()
  declare valuerId: number | null // User who performed the valuation

  @column()
  declare valuationType: 'automated' | 'professional' | 'market_comparison' | 'insurance'

  @column()
  declare currentValue: number

  @column()
  declare previousValue: number | null

  @column()
  declare depreciationRate: number | null

  @column()
  declare marketConditions: string | null

  @column()
  declare valuationMethod: string

  @column()
  declare notes: string | null

  @column()
  declare supportingDocuments: string[] // JSON array of document URLs

  @column()
  declare isVerified: boolean

  @column()
  declare verifiedBy: number | null

  @column()
  declare verifiedAt: DateTime | null

  @column()
  declare expiresAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => Asset)
  declare asset: BelongsTo<typeof Asset>

  @belongsTo(() => User, {
    foreignKey: 'valuerId',
  })
  declare valuer: BelongsTo<typeof User>

  @belongsTo(() => User, {
    foreignKey: 'verifiedBy',
  })
  declare verifier: BelongsTo<typeof User>

  /**
   * Check if valuation is expired
   */
  public get isExpired(): boolean {
    if (!this.expiresAt) return false
    return DateTime.now() > this.expiresAt
  }

  /**
   * Check if valuation is current (not expired)
   */
  public get isCurrent(): boolean {
    return !this.isExpired
  }

  /**
   * Calculate value change percentage
   */
  public get valueChangePercentage(): number | null {
    if (!this.previousValue || this.previousValue === 0) return null
    return ((this.currentValue - this.previousValue) / this.previousValue) * 100
  }

  /**
   * Get value change direction
   */
  public get valueChangeDirection(): 'increased' | 'decreased' | 'unchanged' | null {
    const change = this.valueChangePercentage
    if (change === null) return null
    if (change > 0) return 'increased'
    if (change < 0) return 'decreased'
    return 'unchanged'
  }

  /**
   * Get valuation type display name
   */
  public get valuationTypeDisplay(): string {
    const types = {
      automated: 'Automated Valuation',
      professional: 'Professional Valuation',
      market_comparison: 'Market Comparison',
      insurance: 'Insurance Valuation',
    }
    return types[this.valuationType] || this.valuationType
  }

  /**
   * Check if valuation needs verification
   */
  public get needsVerification(): boolean {
    return !this.isVerified && this.valuationType === 'professional'
  }
}
