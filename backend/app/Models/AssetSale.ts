import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import Asset from './Asset.js'
import User from './User.js'
import Loan from './Loan.js'
import SaleBid from './SaleBid.js'

export default class AssetSale extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare assetId: number

  @column()
  declare sellerId: number // Asset owner

  @column()
  declare loanId: number | null // Associated loan if any

  @column()
  declare saleType: 'borrower_initiated' | 'lender_repossession' | 'voluntary'

  @column()
  declare listingPrice: number

  @column()
  declare reservePrice: number

  @column()
  declare currentHighestBid: number | null

  @column()
  declare finalSalePrice: number | null

  @column()
  declare buyerId: number | null

  @column()
  declare status: 'active' | 'sold' | 'cancelled' | 'expired'

  @column()
  declare auctionStartDate: DateTime

  @column()
  declare auctionEndDate: DateTime

  @column()
  declare description: string

  @column()
  declare terms: Record<string, any> // JSON object with sale terms

  @column()
  declare viewingSchedule: Record<string, any> | null // JSON object with viewing times

  @column()
  declare soldAt: DateTime | null

  @column()
  declare cancelledAt: DateTime | null

  @column()
  declare cancellationReason: string | null

  @column()
  declare proceedsDistributed: boolean

  @column()
  declare distributionDetails: Record<string, any> | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => Asset)
  declare asset: BelongsTo<typeof Asset>

  @belongsTo(() => User, {
    foreignKey: 'sellerId',
  })
  declare seller: BelongsTo<typeof User>

  @belongsTo(() => User, {
    foreignKey: 'buyerId',
  })
  declare buyer: BelongsTo<typeof User>

  @belongsTo(() => Loan)
  declare loan: BelongsTo<typeof Loan>

  @hasMany(() => SaleBid)
  declare bids: HasMany<typeof SaleBid>

  /**
   * Check if auction is active
   */
  public get isActive(): boolean {
    return this.status === 'active' && !this.isExpired
  }

  /**
   * Check if auction has expired
   */
  public get isExpired(): boolean {
    return DateTime.now() > this.auctionEndDate
  }

  /**
   * Check if asset is sold
   */
  public get isSold(): boolean {
    return this.status === 'sold'
  }

  /**
   * Check if auction is cancelled
   */
  public get isCancelled(): boolean {
    return this.status === 'cancelled'
  }

  /**
   * Get time remaining in auction
   */
  public get timeRemaining(): Duration | null {
    if (!this.isActive) return null
    const now = DateTime.now()
    if (now >= this.auctionEndDate) return null
    return this.auctionEndDate.diff(now)
  }

  /**
   * Get days remaining in auction
   */
  public get daysRemaining(): number {
    const remaining = this.timeRemaining
    if (!remaining) return 0
    return Math.ceil(remaining.as('days'))
  }

  /**
   * Check if reserve price is met
   */
  public get isReserveMet(): boolean {
    return this.currentHighestBid !== null && this.currentHighestBid >= this.reservePrice
  }

  /**
   * Calculate potential proceeds for borrower
   */
  public calculateBorrowerProceeds(salePrice: number, outstandingLoan: number = 0): number {
    const netProceeds = salePrice - outstandingLoan
    return Math.max(0, netProceeds)
  }

  /**
   * Get sale type display name
   */
  public get saleTypeDisplay(): string {
    const types = {
      borrower_initiated: 'Borrower Initiated Sale',
      lender_repossession: 'Lender Repossession Sale',
      voluntary: 'Voluntary Sale',
    }
    return types[this.saleType] || this.saleType
  }

  /**
   * Get status display name
   */
  public get statusDisplay(): string {
    const statuses = {
      active: 'Active Auction',
      sold: 'Sold',
      cancelled: 'Cancelled',
      expired: 'Expired',
    }
    return statuses[this.status] || this.status
  }
}
