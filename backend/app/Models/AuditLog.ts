import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './User.js'

export default class AuditLog extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number | null

  @column()
  declare action: string

  @column()
  declare resource: string

  @column()
  declare resourceId: number | null

  @column()
  declare ipAddress: string

  @column()
  declare userAgent: string | null

  @column()
  declare requestData: Record<string, any> | null

  @column()
  declare responseStatus: number

  @column()
  declare duration: number // in milliseconds

  @column()
  declare error: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  /**
   * Check if the action was successful
   */
  public get isSuccessful(): boolean {
    return this.responseStatus >= 200 && this.responseStatus < 300
  }

  /**
   * Check if there was an error
   */
  public get hasError(): boolean {
    return this.error !== null || this.responseStatus >= 400
  }

  /**
   * Get action type (GET, POST, PUT, DELETE)
   */
  public get actionType(): string {
    return this.action.split(' ')[0] || 'UNKNOWN'
  }

  /**
   * Get action path
   */
  public get actionPath(): string {
    const parts = this.action.split(' ')
    return parts.length > 1 ? parts[1] : ''
  }

  /**
   * Format duration for display
   */
  public get formattedDuration(): string {
    if (this.duration < 1000) {
      return `${this.duration}ms`
    }
    return `${(this.duration / 1000).toFixed(2)}s`
  }
}
