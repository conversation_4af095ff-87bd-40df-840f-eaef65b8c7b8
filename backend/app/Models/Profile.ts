import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './User.js'

export default class Profile extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare firstName: string

  @column()
  declare lastName: string

  @column()
  declare middleName: string | null

  @column()
  declare dateOfBirth: DateTime | null

  @column()
  declare gender: 'male' | 'female' | 'other' | null

  @column()
  declare nationalId: string | null

  @column()
  declare passportNumber: string | null

  @column()
  declare kraPin: string | null

  @column()
  declare address: string | null

  @column()
  declare city: string | null

  @column()
  declare county: string | null

  @column()
  declare postalCode: string | null

  @column()
  declare country: string

  @column()
  declare occupation: string | null

  @column()
  declare employer: string | null

  @column()
  declare monthlyIncome: number | null

  @column()
  declare profilePicture: string | null

  @column()
  declare idDocumentFront: string | null

  @column()
  declare idDocumentBack: string | null

  @column()
  declare proofOfIncome: string | null

  @column()
  declare bankStatement: string | null

  @column()
  declare nextOfKinName: string | null

  @column()
  declare nextOfKinPhone: string | null

  @column()
  declare nextOfKinRelationship: string | null

  @column()
  declare emergencyContactName: string | null

  @column()
  declare emergencyContactPhone: string | null

  @column()
  declare businessRegistration: string | null

  @column()
  declare businessLicense: string | null

  @column()
  declare companyName: string | null

  @column()
  declare companyRegistrationNumber: string | null

  @column()
  declare businessType: string | null

  @column()
  declare yearsInBusiness: number | null

  @column()
  declare annualRevenue: number | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  /**
   * Get full name
   */
  public get fullName(): string {
    const parts = [this.firstName, this.middleName, this.lastName].filter(Boolean)
    return parts.join(' ')
  }

  /**
   * Check if profile is complete for individual borrower
   */
  public get isIndividualProfileComplete(): boolean {
    return !!(
      this.firstName &&
      this.lastName &&
      this.dateOfBirth &&
      this.nationalId &&
      this.address &&
      this.city &&
      this.county &&
      this.occupation &&
      this.monthlyIncome &&
      this.idDocumentFront &&
      this.idDocumentBack
    )
  }

  /**
   * Check if profile is complete for business/lender
   */
  public get isBusinessProfileComplete(): boolean {
    return !!(
      this.companyName &&
      this.companyRegistrationNumber &&
      this.businessType &&
      this.businessRegistration &&
      this.businessLicense &&
      this.address &&
      this.city &&
      this.county
    )
  }
}
