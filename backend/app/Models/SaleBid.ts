import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import AssetSale from './AssetSale.js'
import User from './User.js'

export default class SaleBid extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare assetSaleId: number

  @column()
  declare bidderId: number

  @column()
  declare bidAmount: number

  @column()
  declare status: 'active' | 'outbid' | 'winning' | 'withdrawn'

  @column()
  declare isAutoBid: boolean

  @column()
  declare maxAutoBidAmount: number | null

  @column()
  declare bidIncrement: number | null

  @column()
  declare notes: string | null

  @column()
  declare withdrawnAt: DateTime | null

  @column()
  declare withdrawalReason: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => AssetSale)
  declare assetSale: BelongsTo<typeof AssetSale>

  @belongsTo(() => User, {
    foreignKey: 'bidderId',
  })
  declare bidder: BelongsTo<typeof User>

  /**
   * Check if bid is active
   */
  public get isActive(): boolean {
    return this.status === 'active'
  }

  /**
   * Check if bid is winning
   */
  public get isWinning(): boolean {
    return this.status === 'winning'
  }

  /**
   * Check if bid has been outbid
   */
  public get isOutbid(): boolean {
    return this.status === 'outbid'
  }

  /**
   * Check if bid is withdrawn
   */
  public get isWithdrawn(): boolean {
    return this.status === 'withdrawn'
  }

  /**
   * Get status display name
   */
  public get statusDisplay(): string {
    const statuses = {
      active: 'Active',
      outbid: 'Outbid',
      winning: 'Winning',
      withdrawn: 'Withdrawn',
    }
    return statuses[this.status] || this.status
  }

  /**
   * Check if this is an auto bid
   */
  public get hasAutoBid(): boolean {
    return this.isAutoBid && this.maxAutoBidAmount !== null
  }

  /**
   * Get remaining auto bid amount
   */
  public get remainingAutoBidAmount(): number {
    if (!this.hasAutoBid) return 0
    return Math.max(0, this.maxAutoBidAmount! - this.bidAmount)
  }

  /**
   * Calculate next auto bid amount
   */
  public calculateNextAutoBidAmount(currentHighestBid: number): number | null {
    if (!this.hasAutoBid) return null
    
    const increment = this.bidIncrement || 1000 // Default increment
    const nextBid = currentHighestBid + increment
    
    if (nextBid > this.maxAutoBidAmount!) return null
    
    return nextBid
  }
}
