import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './User.js'
import Loan from './Loan.js'
import Asset from './Asset.js'

export default class CoOwnership extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare loanId: number

  @column()
  declare assetId: number

  @column()
  declare borrowerId: number

  @column()
  declare lenderId: number

  @column()
  declare borrowerEquityPercentage: number

  @column()
  declare lenderEquityPercentage: number

  @column()
  declare activatedAt: DateTime

  @column()
  declare principalPaidAtActivation: number

  @column()
  declare assetValueAtActivation: number

  @column()
  declare agreementTerms: Record<string, any> // JSON object with agreement details

  @column()
  declare status: 'active' | 'dissolved' | 'transferred'

  @column()
  declare dissolvedAt: DateTime | null

  @column()
  declare dissolutionReason: string | null

  @column()
  declare transferredAt: DateTime | null

  @column()
  declare transferDetails: Record<string, any> | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => Loan)
  declare loan: BelongsTo<typeof Loan>

  @belongsTo(() => Asset)
  declare asset: BelongsTo<typeof Asset>

  @belongsTo(() => User, {
    foreignKey: 'borrowerId',
  })
  declare borrower: BelongsTo<typeof User>

  @belongsTo(() => User, {
    foreignKey: 'lenderId',
  })
  declare lender: BelongsTo<typeof User>

  /**
   * Check if co-ownership is active
   */
  public get isActive(): boolean {
    return this.status === 'active'
  }

  /**
   * Check if co-ownership is dissolved
   */
  public get isDissolved(): boolean {
    return this.status === 'dissolved'
  }

  /**
   * Check if ownership has been transferred
   */
  public get isTransferred(): boolean {
    return this.status === 'transferred'
  }

  /**
   * Calculate borrower's equity value based on current asset value
   */
  public calculateBorrowerEquityValue(currentAssetValue: number): number {
    return (currentAssetValue * this.borrowerEquityPercentage) / 100
  }

  /**
   * Calculate lender's equity value based on current asset value
   */
  public calculateLenderEquityValue(currentAssetValue: number): number {
    return (currentAssetValue * this.lenderEquityPercentage) / 100
  }

  /**
   * Get agreement summary
   */
  public get agreementSummary(): string {
    return `Borrower: ${this.borrowerEquityPercentage}%, Lender: ${this.lenderEquityPercentage}%`
  }

  /**
   * Calculate days since activation
   */
  public get daysSinceActivation(): number {
    const now = DateTime.now()
    const diff = now.diff(this.activatedAt, 'days')
    return Math.floor(diff.days)
  }

  /**
   * Generate co-ownership agreement
   */
  public static generateAgreementTerms(
    borrowerEquity: number,
    lenderEquity: number,
    assetValue: number,
    principalPaid: number
  ): Record<string, any> {
    return {
      borrowerEquityPercentage: borrowerEquity,
      lenderEquityPercentage: lenderEquity,
      assetValueAtActivation: assetValue,
      principalPaidAtActivation: principalPaid,
      saleRights: {
        borrowerCanInitiateSale: true,
        lenderApprovalRequired: false,
        minimumSalePrice: assetValue * 0.8, // 80% of current value
      },
      proceedsDistribution: {
        borrowerPercentage: borrowerEquity,
        lenderPercentage: lenderEquity,
        outstandingLoanDeduction: true,
      },
      maintenanceResponsibility: 'borrower',
      insuranceRequirement: true,
      disputeResolution: 'arbitration',
      agreementVersion: '1.0',
      generatedAt: DateTime.now().toISO(),
    }
  }
}
