import { DateTime } from 'luxon'
import { BaseModel, column, hasMany, hasOne } from '@adonisjs/lucid/orm'
import type { <PERSON><PERSON><PERSON>, <PERSON>One } from '@adonisjs/lucid/types/relations'
import hash from '@adonisjs/core/services/hash'
import { compose } from '@adonisjs/core/helpers'
import { withAuthFinder } from '@adonisjs/auth/mixins/lucid'
import Profile from './Profile.js'
import Loan from './Loan.js'
import Asset from './Asset.js'

const AuthFinder = withAuthFinder(() => hash.use('scrypt'), {
  uids: ['email', 'phone'],
  passwordColumnName: 'password',
})

export default class User extends compose(BaseModel, AuthFinder) {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare email: string

  @column()
  declare phone: string

  @column({ serializeAs: null })
  declare password: string

  @column()
  declare userType: 'borrower' | 'lender' | 'admin'

  @column()
  declare isVerified: boolean

  @column()
  declare isActive: boolean

  @column()
  declare kycStatus: 'pending' | 'verified' | 'rejected'

  @column()
  declare twoFactorEnabled: boolean

  @column({ serializeAs: null })
  declare twoFactorSecret: string | null

  @column()
  declare lastLoginAt: DateTime | null

  @column()
  declare emailVerifiedAt: DateTime | null

  @column()
  declare phoneVerifiedAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @hasOne(() => Profile)
  declare profile: HasOne<typeof Profile>

  @hasMany(() => Loan, {
    foreignKey: 'borrowerId',
  })
  declare borrowedLoans: HasMany<typeof Loan>

  @hasMany(() => Loan, {
    foreignKey: 'lenderId',
  })
  declare lentLoans: HasMany<typeof Loan>

  @hasMany(() => Asset)
  declare assets: HasMany<typeof Asset>

  /**
   * Check if user is a borrower
   */
  public get isBorrower(): boolean {
    return this.userType === 'borrower'
  }

  /**
   * Check if user is a lender
   */
  public get isLender(): boolean {
    return this.userType === 'lender'
  }

  /**
   * Check if user is an admin
   */
  public get isAdmin(): boolean {
    return this.userType === 'admin'
  }

  /**
   * Check if user has completed KYC
   */
  public get isKycVerified(): boolean {
    return this.kycStatus === 'verified'
  }

  /**
   * Check if user can access the platform
   */
  public get canAccess(): boolean {
    return this.isActive && this.isVerified && this.isKycVerified
  }
}
