import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany, hasOne } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany, HasOne } from '@adonisjs/lucid/types/relations'
import User from './User.js'
import Loan from './Loan.js'
import AssetValuation from './AssetValuation.js'
import AssetSale from './AssetSale.js'

export default class Asset extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare type: 'vehicle' | 'land' | 'luxury_goods' | 'electronics' | 'other'

  @column()
  declare category: string // Car, Motorcycle, Plot, Watch, Phone, etc.

  @column()
  declare name: string

  @column()
  declare description: string

  @column()
  declare brand: string | null

  @column()
  declare model: string | null

  @column()
  declare year: number | null

  @column()
  declare serialNumber: string | null // IMEI, VIN, etc.

  @column()
  declare registrationNumber: string | null // For vehicles

  @column()
  declare logbookNumber: string | null

  @column()
  declare titleDeedNumber: string | null

  @column()
  declare location: string | null

  @column()
  declare size: string | null // For land: acres, sq ft, etc.

  @column()
  declare condition: 'excellent' | 'good' | 'fair' | 'poor'

  @column()
  declare purchasePrice: number | null

  @column()
  declare currentValue: number

  @column()
  declare marketValue: number | null

  @column()
  declare status: 'available' | 'financed' | 'sold' | 'repossessed'

  @column()
  declare images: string[] // JSON array of image URLs

  @column()
  declare documents: string[] // JSON array of document URLs

  @column()
  declare specifications: Record<string, any> // JSON object for asset-specific specs

  @column()
  declare isVerified: boolean

  @column()
  declare verificationNotes: string | null

  @column()
  declare lastValuationDate: DateTime | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare owner: BelongsTo<typeof User>

  @hasMany(() => Loan)
  declare loans: HasMany<typeof Loan>

  @hasMany(() => AssetValuation)
  declare valuations: HasMany<typeof AssetValuation>

  @hasOne(() => AssetSale)
  declare sale: HasOne<typeof AssetSale>

  /**
   * Check if asset is currently financed
   */
  public get isFinanced(): boolean {
    return this.status === 'financed'
  }

  /**
   * Check if asset is available for financing
   */
  public get isAvailable(): boolean {
    return this.status === 'available'
  }

  /**
   * Check if asset has been repossessed
   */
  public get isRepossessed(): boolean {
    return this.status === 'repossessed'
  }

  /**
   * Get the most recent valuation
   */
  public get latestValuation(): number {
    return this.marketValue || this.currentValue
  }

  /**
   * Check if asset needs revaluation (older than 6 months)
   */
  public get needsRevaluation(): boolean {
    if (!this.lastValuationDate) return true
    const sixMonthsAgo = DateTime.now().minus({ months: 6 })
    return this.lastValuationDate < sixMonthsAgo
  }

  /**
   * Get asset display name
   */
  public get displayName(): string {
    const parts = [this.brand, this.model, this.name].filter(Boolean)
    return parts.join(' ') || this.category
  }

  /**
   * Get primary image
   */
  public get primaryImage(): string | null {
    return this.images && this.images.length > 0 ? this.images[0] : null
  }

  /**
   * Check if asset has required documents
   */
  public get hasRequiredDocuments(): boolean {
    if (this.type === 'vehicle') {
      return !!(this.logbookNumber && this.documents && this.documents.length > 0)
    }
    if (this.type === 'land') {
      return !!(this.titleDeedNumber && this.documents && this.documents.length > 0)
    }
    return this.documents && this.documents.length > 0
  }
}
