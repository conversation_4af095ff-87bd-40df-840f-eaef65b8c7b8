import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany, hasOne } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany, HasOne } from '@adonisjs/lucid/types/relations'
import User from './User.js'
import Asset from './Asset.js'
import Payment from './Payment.js'
import CoOwnership from './CoOwnership.js'

export default class Loan extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare loanNumber: string

  @column()
  declare borrowerId: number

  @column()
  declare lenderId: number

  @column()
  declare assetId: number

  @column()
  declare principalAmount: number

  @column()
  declare interestRate: number

  @column()
  declare termMonths: number

  @column()
  declare monthlyPayment: number

  @column()
  declare totalAmount: number

  @column()
  declare outstandingBalance: number

  @column()
  declare principalPaid: number

  @column()
  declare interestPaid: number

  @column()
  declare status: 'pending' | 'approved' | 'active' | 'defaulted' | 'completed' | 'rejected'

  @column()
  declare purpose: string

  @column()
  declare coOwnershipThreshold: number // Percentage (default 50)

  @column()
  declare borrowerEquityPercentage: number // Percentage of sale proceeds for borrower

  @column()
  declare isCoOwnershipActive: boolean

  @column()
  declare nextPaymentDate: DateTime | null

  @column()
  declare lastPaymentDate: DateTime | null

  @column()
  declare defaultDate: DateTime | null

  @column()
  declare approvedAt: DateTime | null

  @column()
  declare disbursedAt: DateTime | null

  @column()
  declare completedAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User, {
    foreignKey: 'borrowerId',
  })
  declare borrower: BelongsTo<typeof User>

  @belongsTo(() => User, {
    foreignKey: 'lenderId',
  })
  declare lender: BelongsTo<typeof User>

  @belongsTo(() => Asset)
  declare asset: BelongsTo<typeof Asset>

  @hasMany(() => Payment)
  declare payments: HasMany<typeof Payment>

  @hasOne(() => CoOwnership)
  declare coOwnership: HasOne<typeof CoOwnership>

  /**
   * Calculate percentage of principal paid
   */
  public get principalPaidPercentage(): number {
    if (this.principalAmount === 0) return 0
    return (this.principalPaid / this.principalAmount) * 100
  }

  /**
   * Check if loan qualifies for co-ownership
   */
  public get qualifiesForCoOwnership(): boolean {
    return this.principalPaidPercentage >= this.coOwnershipThreshold
  }

  /**
   * Check if loan is in default
   */
  public get isInDefault(): boolean {
    return this.status === 'defaulted'
  }

  /**
   * Check if loan is active
   */
  public get isActive(): boolean {
    return this.status === 'active'
  }

  /**
   * Check if loan is completed
   */
  public get isCompleted(): boolean {
    return this.status === 'completed'
  }

  /**
   * Calculate remaining balance
   */
  public get remainingBalance(): number {
    return this.totalAmount - (this.principalPaid + this.interestPaid)
  }

  /**
   * Calculate days overdue
   */
  public get daysOverdue(): number {
    if (!this.nextPaymentDate || this.status !== 'active') return 0
    const today = DateTime.now()
    const diff = today.diff(this.nextPaymentDate, 'days')
    return Math.max(0, Math.floor(diff.days))
  }

  /**
   * Check if payment is overdue
   */
  public get isOverdue(): boolean {
    return this.daysOverdue > 0
  }

  /**
   * Generate loan number
   */
  public static generateLoanNumber(): string {
    const timestamp = Date.now().toString()
    const random = Math.random().toString(36).substring(2, 8).toUpperCase()
    return `HM${timestamp.slice(-6)}${random}`
  }
}
