import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import User from './User.js'
import Loan from './Loan.js'

export default class Payment extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare loanId: number

  @column()
  declare payerId: number

  @column()
  declare amount: number

  @column()
  declare principalAmount: number

  @column()
  declare interestAmount: number

  @column()
  declare penaltyAmount: number

  @column()
  declare paymentMethod: 'mpesa' | 'airtel_money' | 'bank_transfer' | 'cash' | 'other'

  @column()
  declare transactionId: string

  @column()
  declare externalTransactionId: string | null // M-Pesa transaction ID, etc.

  @column()
  declare status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded'

  @column()
  declare paymentType: 'regular' | 'partial' | 'full' | 'penalty' | 'late_fee'

  @column()
  declare reference: string | null

  @column()
  declare notes: string | null

  @column()
  declare processedAt: DateTime | null

  @column()
  declare failureReason: string | null

  @column()
  declare refundedAt: DateTime | null

  @column()
  declare refundReason: string | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => Loan)
  declare loan: BelongsTo<typeof Loan>

  @belongsTo(() => User, {
    foreignKey: 'payerId',
  })
  declare payer: BelongsTo<typeof User>

  /**
   * Check if payment is completed
   */
  public get isCompleted(): boolean {
    return this.status === 'completed'
  }

  /**
   * Check if payment is pending
   */
  public get isPending(): boolean {
    return this.status === 'pending'
  }

  /**
   * Check if payment failed
   */
  public get isFailed(): boolean {
    return this.status === 'failed'
  }

  /**
   * Check if payment was refunded
   */
  public get isRefunded(): boolean {
    return this.status === 'refunded'
  }

  /**
   * Generate transaction ID
   */
  public static generateTransactionId(): string {
    const timestamp = Date.now().toString()
    const random = Math.random().toString(36).substring(2, 8).toUpperCase()
    return `PAY${timestamp.slice(-8)}${random}`
  }

  /**
   * Get payment method display name
   */
  public get paymentMethodDisplay(): string {
    const methods = {
      mpesa: 'M-Pesa',
      airtel_money: 'Airtel Money',
      bank_transfer: 'Bank Transfer',
      cash: 'Cash',
      other: 'Other',
    }
    return methods[this.paymentMethod] || this.paymentMethod
  }

  /**
   * Get status display name
   */
  public get statusDisplay(): string {
    const statuses = {
      pending: 'Pending',
      completed: 'Completed',
      failed: 'Failed',
      cancelled: 'Cancelled',
      refunded: 'Refunded',
    }
    return statuses[this.status] || this.status
  }
}
