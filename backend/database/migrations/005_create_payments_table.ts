import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'payments'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.integer('loan_id').unsigned().references('id').inTable('loans').onDelete('CASCADE')
      table.integer('payer_id').unsigned().references('id').inTable('users').onDelete('CASCADE')
      
      // Payment Details
      table.decimal('amount', 15, 2).notNullable()
      table.decimal('principal_amount', 15, 2).notNullable()
      table.decimal('interest_amount', 15, 2).notNullable()
      table.decimal('penalty_amount', 15, 2).defaultTo(0)
      
      // Payment Method
      table.enum('payment_method', ['mpesa', 'airtel_money', 'bank_transfer', 'cash', 'other']).notNullable()
      table.string('transaction_id').unique().notNullable()
      table.string('external_transaction_id').nullable() // M-Pesa transaction ID, etc.
      
      // Status and Type
      table.enum('status', ['pending', 'completed', 'failed', 'cancelled', 'refunded']).defaultTo('pending')
      table.enum('payment_type', ['regular', 'partial', 'full', 'penalty', 'late_fee']).defaultTo('regular')
      
      // Additional Information
      table.string('reference').nullable()
      table.text('notes').nullable()
      table.timestamp('processed_at').nullable()
      table.text('failure_reason').nullable()
      table.timestamp('refunded_at').nullable()
      table.text('refund_reason').nullable()
      
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()

      // Indexes
      table.index(['loan_id'])
      table.index(['payer_id'])
      table.index(['transaction_id'])
      table.index(['external_transaction_id'])
      table.index(['status'])
      table.index(['payment_method'])
      table.index(['processed_at'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
