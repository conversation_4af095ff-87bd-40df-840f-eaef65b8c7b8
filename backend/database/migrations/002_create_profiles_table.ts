import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'profiles'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.integer('user_id').unsigned().references('id').inTable('users').onDelete('CASCADE')
      
      // Personal Information
      table.string('first_name').nullable()
      table.string('last_name').nullable()
      table.string('middle_name').nullable()
      table.date('date_of_birth').nullable()
      table.enum('gender', ['male', 'female', 'other']).nullable()
      
      // Identification
      table.string('national_id').nullable()
      table.string('passport_number').nullable()
      table.string('kra_pin').nullable()
      
      // Address Information
      table.text('address').nullable()
      table.string('city').nullable()
      table.string('county').nullable()
      table.string('postal_code').nullable()
      table.string('country').defaultTo('Kenya')
      
      // Employment Information
      table.string('occupation').nullable()
      table.string('employer').nullable()
      table.decimal('monthly_income', 15, 2).nullable()
      
      // Documents
      table.string('profile_picture').nullable()
      table.string('id_document_front').nullable()
      table.string('id_document_back').nullable()
      table.string('proof_of_income').nullable()
      table.string('bank_statement').nullable()
      
      // Emergency Contacts
      table.string('next_of_kin_name').nullable()
      table.string('next_of_kin_phone').nullable()
      table.string('next_of_kin_relationship').nullable()
      table.string('emergency_contact_name').nullable()
      table.string('emergency_contact_phone').nullable()
      
      // Business Information (for lenders)
      table.string('business_registration').nullable()
      table.string('business_license').nullable()
      table.string('company_name').nullable()
      table.string('company_registration_number').nullable()
      table.string('business_type').nullable()
      table.integer('years_in_business').nullable()
      table.decimal('annual_revenue', 15, 2).nullable()
      
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()

      // Indexes
      table.index(['user_id'])
      table.index(['national_id'])
      table.index(['company_registration_number'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
