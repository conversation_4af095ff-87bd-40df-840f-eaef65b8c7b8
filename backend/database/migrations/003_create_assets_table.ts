import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'assets'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.integer('user_id').unsigned().references('id').inTable('users').onDelete('CASCADE')
      
      // Asset Classification
      table.enum('type', ['vehicle', 'land', 'luxury_goods', 'electronics', 'other']).notNullable()
      table.string('category').notNullable() // Car, Motorcycle, Plot, Watch, Phone, etc.
      table.string('name').notNullable()
      table.text('description').notNullable()
      
      // Asset Details
      table.string('brand').nullable()
      table.string('model').nullable()
      table.integer('year').nullable()
      table.string('serial_number').nullable() // IMEI, VIN, etc.
      table.string('registration_number').nullable() // For vehicles
      table.string('logbook_number').nullable()
      table.string('title_deed_number').nullable()
      table.string('location').nullable()
      table.string('size').nullable() // For land: acres, sq ft, etc.
      
      // Condition and Value
      table.enum('condition', ['excellent', 'good', 'fair', 'poor']).notNullable()
      table.decimal('purchase_price', 15, 2).nullable()
      table.decimal('current_value', 15, 2).notNullable()
      table.decimal('market_value', 15, 2).nullable()
      
      // Status
      table.enum('status', ['available', 'financed', 'sold', 'repossessed']).defaultTo('available')
      
      // Media and Documents
      table.json('images').nullable() // Array of image URLs
      table.json('documents').nullable() // Array of document URLs
      table.json('specifications').nullable() // Asset-specific specifications
      
      // Verification
      table.boolean('is_verified').defaultTo(false)
      table.text('verification_notes').nullable()
      table.timestamp('last_valuation_date').nullable()
      
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()

      // Indexes
      table.index(['user_id'])
      table.index(['type'])
      table.index(['status'])
      table.index(['registration_number'])
      table.index(['serial_number'])
      table.index(['logbook_number'])
      table.index(['title_deed_number'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
