import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'co_ownerships'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.integer('loan_id').unsigned().references('id').inTable('loans').onDelete('CASCADE')
      table.integer('asset_id').unsigned().references('id').inTable('assets').onDelete('CASCADE')
      table.integer('borrower_id').unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.integer('lender_id').unsigned().references('id').inTable('users').onDelete('CASCADE')
      
      // Equity Distribution
      table.decimal('borrower_equity_percentage', 5, 2).notNullable()
      table.decimal('lender_equity_percentage', 5, 2).notNullable()
      
      // Activation Details
      table.timestamp('activated_at').notNullable()
      table.decimal('principal_paid_at_activation', 15, 2).notNullable()
      table.decimal('asset_value_at_activation', 15, 2).notNullable()
      
      // Agreement Terms
      table.json('agreement_terms').notNullable() // JSON object with detailed terms
      
      // Status
      table.enum('status', ['active', 'dissolved', 'transferred']).defaultTo('active')
      table.timestamp('dissolved_at').nullable()
      table.text('dissolution_reason').nullable()
      table.timestamp('transferred_at').nullable()
      table.json('transfer_details').nullable()
      
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()

      // Indexes
      table.index(['loan_id'])
      table.index(['asset_id'])
      table.index(['borrower_id'])
      table.index(['lender_id'])
      table.index(['status'])
      table.index(['activated_at'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
