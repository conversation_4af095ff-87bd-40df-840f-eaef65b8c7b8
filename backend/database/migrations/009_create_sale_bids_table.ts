import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'sale_bids'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.integer('asset_sale_id').unsigned().references('id').inTable('asset_sales').onDelete('CASCADE')
      table.integer('bidder_id').unsigned().references('id').inTable('users').onDelete('CASCADE')
      
      // Bid Details
      table.decimal('bid_amount', 15, 2).notNullable()
      table.enum('status', ['active', 'outbid', 'winning', 'withdrawn']).defaultTo('active')
      
      // Auto Bidding
      table.boolean('is_auto_bid').defaultTo(false)
      table.decimal('max_auto_bid_amount', 15, 2).nullable()
      table.decimal('bid_increment', 15, 2).nullable()
      
      // Additional Information
      table.text('notes').nullable()
      table.timestamp('withdrawn_at').nullable()
      table.text('withdrawal_reason').nullable()
      
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()

      // Indexes
      table.index(['asset_sale_id'])
      table.index(['bidder_id'])
      table.index(['status'])
      table.index(['bid_amount'])
      table.index(['is_auto_bid'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
