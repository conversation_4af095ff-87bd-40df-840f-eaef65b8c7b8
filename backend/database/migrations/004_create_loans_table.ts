import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'loans'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.string('loan_number').unique().notNullable()
      table.integer('borrower_id').unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.integer('lender_id').unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.integer('asset_id').unsigned().references('id').inTable('assets').onDelete('CASCADE')
      
      // Loan Terms
      table.decimal('principal_amount', 15, 2).notNullable()
      table.decimal('interest_rate', 5, 2).notNullable() // Annual percentage rate
      table.integer('term_months').notNullable()
      table.decimal('monthly_payment', 15, 2).notNullable()
      table.decimal('total_amount', 15, 2).notNullable()
      
      // Payment Tracking
      table.decimal('outstanding_balance', 15, 2).notNullable()
      table.decimal('principal_paid', 15, 2).defaultTo(0)
      table.decimal('interest_paid', 15, 2).defaultTo(0)
      
      // Status
      table.enum('status', ['pending', 'approved', 'active', 'defaulted', 'completed', 'rejected']).defaultTo('pending')
      table.text('purpose').notNullable()
      
      // Co-ownership Configuration
      table.decimal('co_ownership_threshold', 5, 2).defaultTo(50) // Percentage
      table.decimal('borrower_equity_percentage', 5, 2).defaultTo(50) // Percentage of sale proceeds
      table.boolean('is_co_ownership_active').defaultTo(false)
      
      // Important Dates
      table.timestamp('next_payment_date').nullable()
      table.timestamp('last_payment_date').nullable()
      table.timestamp('default_date').nullable()
      table.timestamp('approved_at').nullable()
      table.timestamp('disbursed_at').nullable()
      table.timestamp('completed_at').nullable()
      
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()

      // Indexes
      table.index(['loan_number'])
      table.index(['borrower_id'])
      table.index(['lender_id'])
      table.index(['asset_id'])
      table.index(['status'])
      table.index(['next_payment_date'])
      table.index(['is_co_ownership_active'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
