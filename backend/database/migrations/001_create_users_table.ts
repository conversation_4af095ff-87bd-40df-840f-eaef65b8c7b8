import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'users'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.string('email').unique().notNullable()
      table.string('phone').unique().notNullable()
      table.string('password').notNullable()
      table.enum('user_type', ['borrower', 'lender', 'admin']).notNullable()
      table.boolean('is_verified').defaultTo(false)
      table.boolean('is_active').defaultTo(true)
      table.enum('kyc_status', ['pending', 'verified', 'rejected']).defaultTo('pending')
      table.boolean('two_factor_enabled').defaultTo(false)
      table.string('two_factor_secret').nullable()
      table.timestamp('last_login_at').nullable()
      table.timestamp('email_verified_at').nullable()
      table.timestamp('phone_verified_at').nullable()
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()

      // Indexes
      table.index(['email'])
      table.index(['phone'])
      table.index(['user_type'])
      table.index(['kyc_status'])
      table.index(['is_active'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
