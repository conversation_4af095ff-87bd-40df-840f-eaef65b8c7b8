import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'asset_valuations'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.integer('asset_id').unsigned().references('id').inTable('assets').onDelete('CASCADE')
      table.integer('valuer_id').unsigned().references('id').inTable('users').nullable()
      
      // Valuation Details
      table.enum('valuation_type', ['automated', 'professional', 'market_comparison', 'insurance']).notNullable()
      table.decimal('current_value', 15, 2).notNullable()
      table.decimal('previous_value', 15, 2).nullable()
      table.decimal('depreciation_rate', 5, 2).nullable()
      
      // Market Information
      table.text('market_conditions').nullable()
      table.string('valuation_method').notNullable()
      table.text('notes').nullable()
      table.json('supporting_documents').nullable() // Array of document URLs
      
      // Verification
      table.boolean('is_verified').defaultTo(false)
      table.integer('verified_by').unsigned().references('id').inTable('users').nullable()
      table.timestamp('verified_at').nullable()
      table.timestamp('expires_at').nullable()
      
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()

      // Indexes
      table.index(['asset_id'])
      table.index(['valuer_id'])
      table.index(['valuation_type'])
      table.index(['is_verified'])
      table.index(['expires_at'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
