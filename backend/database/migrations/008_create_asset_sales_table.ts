import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'asset_sales'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.integer('asset_id').unsigned().references('id').inTable('assets').onDelete('CASCADE')
      table.integer('seller_id').unsigned().references('id').inTable('users').onDelete('CASCADE')
      table.integer('loan_id').unsigned().references('id').inTable('loans').nullable()
      
      // Sale Details
      table.enum('sale_type', ['borrower_initiated', 'lender_repossession', 'voluntary']).notNullable()
      table.decimal('listing_price', 15, 2).notNullable()
      table.decimal('reserve_price', 15, 2).notNullable()
      table.decimal('current_highest_bid', 15, 2).nullable()
      table.decimal('final_sale_price', 15, 2).nullable()
      table.integer('buyer_id').unsigned().references('id').inTable('users').nullable()
      
      // Status and Timing
      table.enum('status', ['active', 'sold', 'cancelled', 'expired']).defaultTo('active')
      table.timestamp('auction_start_date').notNullable()
      table.timestamp('auction_end_date').notNullable()
      
      // Additional Information
      table.text('description').notNullable()
      table.json('terms').notNullable() // Sale terms and conditions
      table.json('viewing_schedule').nullable() // Viewing times and locations
      
      // Completion Details
      table.timestamp('sold_at').nullable()
      table.timestamp('cancelled_at').nullable()
      table.text('cancellation_reason').nullable()
      table.boolean('proceeds_distributed').defaultTo(false)
      table.json('distribution_details').nullable()
      
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()

      // Indexes
      table.index(['asset_id'])
      table.index(['seller_id'])
      table.index(['buyer_id'])
      table.index(['loan_id'])
      table.index(['status'])
      table.index(['sale_type'])
      table.index(['auction_end_date'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
