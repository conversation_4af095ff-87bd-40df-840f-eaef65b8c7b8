import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'audit_logs'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id').primary()
      table.integer('user_id').unsigned().references('id').inTable('users').nullable()
      
      // Action Details
      table.string('action').notNullable()
      table.string('resource').notNullable()
      table.integer('resource_id').nullable()
      
      // Request Information
      table.string('ip_address').notNullable()
      table.text('user_agent').nullable()
      table.json('request_data').nullable()
      
      // Response Information
      table.integer('response_status').notNullable()
      table.integer('duration').notNullable() // in milliseconds
      table.text('error').nullable()
      
      table.timestamp('created_at').notNullable()
      table.timestamp('updated_at').notNullable()

      // Indexes
      table.index(['user_id'])
      table.index(['action'])
      table.index(['resource'])
      table.index(['resource_id'])
      table.index(['ip_address'])
      table.index(['response_status'])
      table.index(['created_at'])
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
