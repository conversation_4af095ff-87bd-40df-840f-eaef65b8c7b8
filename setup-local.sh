#!/bin/bash

# Ha<PERSON><PERSON>ali Local Development Setup Script
echo "🚀 Setting up Ha<PERSON><PERSON>ali for Local Development..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

print_success "Node.js $(node -v) is available ✓"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_success "npm $(npm -v) is available ✓"

# Clean npm cache
print_status "Cleaning npm cache..."
npm cache clean --force

# Remove existing node_modules and package-lock.json files
print_status "Cleaning existing installations..."
rm -rf backend/node_modules backend/package-lock.json
rm -rf admin-panel/node_modules admin-panel/package-lock.json
rm -rf mpesa-miniapp/node_modules mpesa-miniapp/package-lock.json
rm -rf mobile/node_modules mobile/package-lock.json

print_success "Cleaned existing installations ✓"

# Install backend dependencies
print_status "Installing backend dependencies..."
cd backend
if npm install; then
    print_success "Backend dependencies installed ✓"
else
    print_error "Failed to install backend dependencies"
    exit 1
fi
cd ..

# Install admin panel dependencies
print_status "Installing admin panel dependencies..."
cd admin-panel
if npm install; then
    print_success "Admin panel dependencies installed ✓"
else
    print_error "Failed to install admin panel dependencies"
    exit 1
fi
cd ..

# Install M-Pesa mini app dependencies
print_status "Installing M-Pesa mini app dependencies..."
cd mpesa-miniapp
if npm install; then
    print_success "M-Pesa mini app dependencies installed ✓"
else
    print_error "Failed to install M-Pesa mini app dependencies"
    exit 1
fi
cd ..

# Install mobile app dependencies (optional)
print_status "Installing mobile app dependencies..."
cd mobile
if npm install; then
    print_success "Mobile app dependencies installed ✓"
else
    print_warning "Failed to install mobile app dependencies (this is optional for local web development)"
fi
cd ..

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p backend/uploads
mkdir -p backend/tmp
mkdir -p logs

# Check if PostgreSQL is available
print_status "Checking database requirements..."
if command -v psql &> /dev/null; then
    print_success "PostgreSQL client is available ✓"
    print_status "Make sure PostgreSQL server is running and create the database:"
    echo "  createdb hakimali_dev"
    echo "  psql hakimali_dev -c \"CREATE USER hakimali WITH PASSWORD 'hakimali_password';\""
    echo "  psql hakimali_dev -c \"GRANT ALL PRIVILEGES ON DATABASE hakimali_dev TO hakimali;\""
else
    print_warning "PostgreSQL client not found. You'll need to install PostgreSQL or use Docker for the database."
fi

# Check if Redis is available
if command -v redis-cli &> /dev/null; then
    print_success "Redis client is available ✓"
    print_status "Make sure Redis server is running: redis-server"
else
    print_warning "Redis client not found. You'll need to install Redis or use Docker for caching."
fi

# Create local development scripts
print_status "Creating development scripts..."

# Backend development script
cat > start-backend.sh << 'EOF'
#!/bin/bash
echo "🔧 Starting Backend API (AdonisJS)..."
cd backend
npm run dev
EOF
chmod +x start-backend.sh

# Admin panel development script
cat > start-admin.sh << 'EOF'
#!/bin/bash
echo "🔧 Starting Admin Panel (Next.js)..."
cd admin-panel
npm run dev
EOF
chmod +x start-admin.sh

# M-Pesa mini app development script
cat > start-mpesa.sh << 'EOF'
#!/bin/bash
echo "🔧 Starting M-Pesa Mini App (Next.js)..."
cd mpesa-miniapp
npm run dev
EOF
chmod +x start-mpesa.sh

# Mobile app development script
cat > start-mobile.sh << 'EOF'
#!/bin/bash
echo "🔧 Starting Mobile App (Expo)..."
cd mobile
npm start
EOF
chmod +x start-mobile.sh

# Create a comprehensive start script for all services
cat > start-all-local.sh << 'EOF'
#!/bin/bash

echo "🚀 Starting HakiMali Platform (Local Development)..."

# Function to run command in background and track PID
run_service() {
    local name=$1
    local script=$2
    echo "Starting $name..."
    ./$script &
    local pid=$!
    echo "$pid" > "${name}.pid"
    echo "$name started with PID $pid"
}

# Start all services
run_service "backend" "start-backend.sh"
sleep 3
run_service "admin-panel" "start-admin.sh"
sleep 2
run_service "mpesa-miniapp" "start-mpesa.sh"

echo ""
echo "🎉 All services are starting up!"
echo ""
echo "📱 Access your applications:"
echo "   🔗 Backend API: http://localhost:3333"
echo "   🔗 Admin Panel: http://localhost:3000"
echo "   🔗 M-Pesa Mini App: http://localhost:3001"
echo ""
echo "📊 Monitor logs in separate terminals:"
echo "   Backend: cd backend && npm run dev"
echo "   Admin: cd admin-panel && npm run dev"
echo "   M-Pesa: cd mpesa-miniapp && npm run dev"
echo ""
echo "🛑 Stop all services:"
echo "   ./stop-all-local.sh"
EOF
chmod +x start-all-local.sh

# Create stop script
cat > stop-all-local.sh << 'EOF'
#!/bin/bash

echo "🛑 Stopping HakiMali Platform..."

# Function to stop service by PID file
stop_service() {
    local name=$1
    if [ -f "${name}.pid" ]; then
        local pid=$(cat "${name}.pid")
        if kill -0 $pid 2>/dev/null; then
            echo "Stopping $name (PID: $pid)..."
            kill $pid
            rm "${name}.pid"
        else
            echo "$name is not running"
            rm "${name}.pid" 2>/dev/null
        fi
    else
        echo "No PID file found for $name"
    fi
}

# Stop all services
stop_service "backend"
stop_service "admin-panel"
stop_service "mpesa-miniapp"

# Also kill any remaining node processes on our ports
echo "Cleaning up any remaining processes..."
lsof -ti:3333 | xargs kill -9 2>/dev/null || true
lsof -ti:3000 | xargs kill -9 2>/dev/null || true
lsof -ti:3001 | xargs kill -9 2>/dev/null || true

echo "✅ All services stopped"
EOF
chmod +x stop-all-local.sh

print_success "Development scripts created ✓"

print_success "Local development setup completed! 🎉"
echo ""
echo "📋 Next Steps:"
echo ""
echo "1. 🗄️  Set up your databases:"
echo "   PostgreSQL: Create 'hakimali_dev' database"
echo "   Redis: Start Redis server"
echo ""
echo "2. ⚙️  Update environment variables:"
echo "   - backend/.env (database connections, API keys)"
echo "   - admin-panel/.env.local"
echo "   - mpesa-miniapp/.env.local"
echo ""
echo "3. 🚀 Start development:"
echo "   Individual services:"
echo "     ./start-backend.sh     # Backend API"
echo "     ./start-admin.sh       # Admin Panel"
echo "     ./start-mpesa.sh       # M-Pesa Mini App"
echo "     ./start-mobile.sh      # Mobile App (Expo)"
echo ""
echo "   All web services at once:"
echo "     ./start-all-local.sh"
echo ""
echo "4. 🛑 Stop services:"
echo "     ./stop-all-local.sh"
echo ""
echo "📱 Application URLs:"
echo "   Backend API: http://localhost:3333"
echo "   Admin Panel: http://localhost:3000"
echo "   M-Pesa Mini App: http://localhost:3001"
echo ""
print_warning "⚠️  Make sure PostgreSQL and Redis are running before starting the backend!"
echo ""
echo "🎯 Happy local development with HakiMali!"
EOF
