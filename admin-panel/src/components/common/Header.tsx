import React from 'react'
import { useRouter } from 'next/router'
import { BellIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline'
import { useAuthStore } from '@/store/authStore'

const Header: React.FC = () => {
  const router = useRouter()
  const { user } = useAuthStore()

  const getPageTitle = () => {
    const path = router.pathname
    switch (path) {
      case '/':
        return 'Dashboard'
      case '/users':
        return 'Users'
      case '/loans':
        return 'Loans'
      case '/assets':
        return 'Assets'
      case '/payments':
        return 'Payments'
      case '/sales':
        return 'Asset Sales'
      case '/reports':
        return 'Reports'
      case '/settings':
        return 'Settings'
      default:
        return 'HakiMali Admin'
    }
  }

  return (
    <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
      <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
        <div className="flex flex-1 items-center">
          <h1 className="text-xl font-semibold text-gray-900">{getPageTitle()}</h1>
        </div>
        
        <div className="flex items-center gap-x-4 lg:gap-x-6">
          {/* Search */}
          <div className="hidden lg:block">
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
              </div>
              <input
                id="search"
                name="search"
                className="block w-full rounded-md border-0 bg-gray-50 py-1.5 pl-10 pr-3 text-gray-900 placeholder:text-gray-400 focus:bg-white focus:ring-2 focus:ring-primary-600 sm:text-sm sm:leading-6"
                placeholder="Search..."
                type="search"
              />
            </div>
          </div>

          {/* Notifications */}
          <button
            type="button"
            className="relative rounded-full bg-white p-1 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2"
          >
            <span className="sr-only">View notifications</span>
            <BellIcon className="h-6 w-6" aria-hidden="true" />
            {/* Notification badge */}
            <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center">
              3
            </span>
          </button>

          {/* Separator */}
          <div className="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200" aria-hidden="true" />

          {/* Profile */}
          <div className="flex items-center gap-x-2">
            <div className="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
              <span className="text-white text-sm font-medium">
                {user?.profile?.firstName?.[0] || user?.email?.[0]?.toUpperCase() || 'A'}
              </span>
            </div>
            <div className="hidden lg:block">
              <p className="text-sm font-medium text-gray-900">
                {user?.profile?.firstName || 'Admin'}
              </p>
              <p className="text-xs text-gray-500">{user?.email}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Header
