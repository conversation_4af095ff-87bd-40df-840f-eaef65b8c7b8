import React, { ReactNode, useEffect } from 'react'
import { useRouter } from 'next/router'
import Sidebar from './Sidebar'
import Header from './Header'
import { useAuthStore } from '@/store/authStore'

interface LayoutProps {
  children: ReactNode
  title?: string
}

const Layout: React.FC<LayoutProps> = ({ children, title = 'HakiMali Admin' }) => {
  const router = useRouter()
  const { isAuthenticated, isLoading, getCurrentUser } = useAuthStore()

  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      router.push('/login')
    } else if (isAuthenticated) {
      getCurrentUser()
    }
  }, [isAuthenticated, isLoading, router, getCurrentUser])

  useEffect(() => {
    document.title = title
  }, [title])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar />
      <div className="lg:pl-64">
        <Header />
        <main className="py-6">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

export default Layout
