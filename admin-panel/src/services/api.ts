import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { ApiResponse, PaginatedResponse } from '@/types'

class ApiService {
  private api: AxiosInstance

  constructor() {
    this.api = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3333',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = this.getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.removeToken()
          window.location.href = '/login'
        }
        return Promise.reject(error)
      }
    )
  }

  private getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('hakimali_admin_token')
    }
    return null
  }

  private setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('hakimali_admin_token', token)
    }
  }

  private removeToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('hakimali_admin_token')
    }
  }

  // Authentication
  async login(email: string, password: string): Promise<ApiResponse<{ user: any; token: string }>> {
    const response = await this.api.post('/api/v1/auth/login', { email, password })
    if (response.data.success && response.data.data.token) {
      this.setToken(response.data.data.token)
    }
    return response.data
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/api/v1/auth/logout')
    } finally {
      this.removeToken()
    }
  }

  async getCurrentUser(): Promise<ApiResponse<{ user: any }>> {
    const response = await this.api.get('/api/v1/users/me')
    return response.data
  }

  // Dashboard
  async getDashboardStats(): Promise<ApiResponse<any>> {
    const response = await this.api.get('/api/v1/admin/dashboard')
    return response.data
  }

  // Users
  async getUsers(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await this.api.get('/api/v1/admin/users', { params })
    return response.data
  }

  async getUser(id: number): Promise<ApiResponse<{ user: any }>> {
    const response = await this.api.get(`/api/v1/admin/users/${id}`)
    return response.data
  }

  async verifyUserKyc(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.post(`/api/v1/admin/users/${id}/verify-kyc`)
    return response.data
  }

  async suspendUser(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.post(`/api/v1/admin/users/${id}/suspend`)
    return response.data
  }

  async activateUser(id: number): Promise<ApiResponse<any>> {
    const response = await this.api.post(`/api/v1/admin/users/${id}/activate`)
    return response.data
  }

  // Loans
  async getLoans(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await this.api.get('/api/v1/admin/loans', { params })
    return response.data
  }

  async getLoan(id: number): Promise<ApiResponse<{ loan: any }>> {
    const response = await this.api.get(`/api/v1/loans/${id}`)
    return response.data
  }

  async getLoanPayments(id: number): Promise<ApiResponse<{ payments: any[] }>> {
    const response = await this.api.get(`/api/v1/loans/${id}/payments`)
    return response.data
  }

  // Assets
  async getAssets(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await this.api.get('/api/v1/admin/assets', { params })
    return response.data
  }

  async getAsset(id: number): Promise<ApiResponse<{ asset: any }>> {
    const response = await this.api.get(`/api/v1/assets/${id}`)
    return response.data
  }

  async getAssetValuations(id: number): Promise<ApiResponse<{ valuations: any[] }>> {
    const response = await this.api.get(`/api/v1/assets/${id}/valuations`)
    return response.data
  }

  // Payments
  async getPayments(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await this.api.get('/api/v1/admin/payments', { params })
    return response.data
  }

  async getPayment(id: number): Promise<ApiResponse<{ payment: any }>> {
    const response = await this.api.get(`/api/v1/payments/${id}`)
    return response.data
  }

  // Asset Sales
  async getAssetSales(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await this.api.get('/api/v1/admin/sales', { params })
    return response.data
  }

  async getAssetSale(id: number): Promise<ApiResponse<{ sale: any }>> {
    const response = await this.api.get(`/api/v1/sales/${id}`)
    return response.data
  }

  async getAssetSaleBids(id: number): Promise<ApiResponse<{ bids: any[] }>> {
    const response = await this.api.get(`/api/v1/sales/${id}/bids`)
    return response.data
  }

  // Co-ownership
  async getCoOwnerships(params?: any): Promise<ApiResponse<PaginatedResponse<any>>> {
    const response = await this.api.get('/api/v1/co-ownership', { params })
    return response.data
  }

  async getCoOwnership(id: number): Promise<ApiResponse<{ coOwnership: any }>> {
    const response = await this.api.get(`/api/v1/co-ownership/${id}`)
    return response.data
  }

  // Reports
  async getLoanReports(params?: any): Promise<ApiResponse<any>> {
    const response = await this.api.get('/api/v1/admin/reports/loans', { params })
    return response.data
  }

  async getPaymentReports(params?: any): Promise<ApiResponse<any>> {
    const response = await this.api.get('/api/v1/admin/reports/payments', { params })
    return response.data
  }

  async getAssetReports(params?: any): Promise<ApiResponse<any>> {
    const response = await this.api.get('/api/v1/admin/reports/assets', { params })
    return response.data
  }

  // Generic methods
  async get<T>(url: string, params?: any): Promise<AxiosResponse<T>> {
    return this.api.get(url, { params })
  }

  async post<T>(url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.api.post(url, data)
  }

  async put<T>(url: string, data?: any): Promise<AxiosResponse<T>> {
    return this.api.put(url, data)
  }

  async delete<T>(url: string): Promise<AxiosResponse<T>> {
    return this.api.delete(url)
  }
}

export const apiService = new ApiService()
export default apiService
