import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User } from '@/types'
import apiService from '@/services/api'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  login: (email: string, password: string) => Promise<boolean>
  logout: () => Promise<void>
  getCurrentUser: () => Promise<void>
  clearError: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await apiService.login(email, password)
          
          if (response.success && response.data) {
            const { user } = response.data
            
            // Only allow admin users
            if (user.userType !== 'admin') {
              set({ 
                isLoading: false, 
                error: 'Access denied. Admin privileges required.',
                isAuthenticated: false,
                user: null 
              })
              return false
            }

            set({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
            return true
          } else {
            set({
              isLoading: false,
              error: response.message || 'Login failed',
              isAuthenticated: false,
              user: null,
            })
            return false
          }
        } catch (error: any) {
          const errorMessage = error.response?.data?.message || 'Login failed'
          set({
            isLoading: false,
            error: errorMessage,
            isAuthenticated: false,
            user: null,
          })
          return false
        }
      },

      logout: async () => {
        set({ isLoading: true })
        
        try {
          await apiService.logout()
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          })
        }
      },

      getCurrentUser: async () => {
        set({ isLoading: true })
        
        try {
          const response = await apiService.getCurrentUser()
          
          if (response.success && response.data) {
            const { user } = response.data
            
            // Verify user is still admin
            if (user.userType !== 'admin') {
              await get().logout()
              return
            }

            set({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
          } else {
            await get().logout()
          }
        } catch (error: any) {
          console.error('Get current user error:', error)
          await get().logout()
        }
      },

      clearError: () => {
        set({ error: null })
      },
    }),
    {
      name: 'hakimali-admin-auth',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
