// User Types
export interface User {
  id: number
  email: string
  phone: string
  userType: 'borrower' | 'lender' | 'admin'
  isVerified: boolean
  isActive: boolean
  kycStatus: 'pending' | 'verified' | 'rejected'
  twoFactorEnabled: boolean
  lastLoginAt: string | null
  emailVerifiedAt: string | null
  phoneVerifiedAt: string | null
  createdAt: string
  updatedAt: string
  profile?: Profile
}

export interface Profile {
  id: number
  userId: number
  firstName?: string
  lastName?: string
  middleName?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other'
  nationalId?: string
  passportNumber?: string
  kraPin?: string
  address?: string
  city?: string
  county?: string
  postalCode?: string
  country: string
  occupation?: string
  employer?: string
  monthlyIncome?: number
  profilePicture?: string
  idDocumentFront?: string
  idDocumentBack?: string
  proofOfIncome?: string
  bankStatement?: string
  nextOfKinName?: string
  nextOfKinPhone?: string
  nextOfKinRelationship?: string
  emergencyContactName?: string
  emergencyContactPhone?: string
  businessRegistration?: string
  businessLicense?: string
  companyName?: string
  companyRegistrationNumber?: string
  businessType?: string
  yearsInBusiness?: number
  annualRevenue?: number
  createdAt: string
  updatedAt: string
}

// Asset Types
export interface Asset {
  id: number
  userId: number
  type: 'vehicle' | 'land' | 'luxury_goods' | 'electronics' | 'other'
  category: string
  name: string
  description: string
  brand?: string
  model?: string
  year?: number
  serialNumber?: string
  registrationNumber?: string
  logbookNumber?: string
  titleDeedNumber?: string
  location?: string
  size?: string
  condition: 'excellent' | 'good' | 'fair' | 'poor'
  purchasePrice?: number
  currentValue: number
  marketValue?: number
  status: 'available' | 'financed' | 'sold' | 'repossessed'
  images?: string[]
  documents?: string[]
  specifications?: Record<string, any>
  isVerified: boolean
  verificationNotes?: string
  lastValuationDate?: string
  createdAt: string
  updatedAt: string
  owner?: User
  valuations?: AssetValuation[]
  loans?: Loan[]
}

// Loan Types
export interface Loan {
  id: number
  loanNumber: string
  borrowerId: number
  lenderId: number
  assetId: number
  principalAmount: number
  interestRate: number
  termMonths: number
  monthlyPayment: number
  totalAmount: number
  outstandingBalance: number
  principalPaid: number
  interestPaid: number
  status: 'pending' | 'approved' | 'active' | 'defaulted' | 'completed' | 'rejected'
  purpose: string
  coOwnershipThreshold: number
  borrowerEquityPercentage: number
  isCoOwnershipActive: boolean
  nextPaymentDate?: string
  lastPaymentDate?: string
  defaultDate?: string
  approvedAt?: string
  disbursedAt?: string
  completedAt?: string
  createdAt: string
  updatedAt: string
  borrower?: User
  lender?: User
  asset?: Asset
  payments?: Payment[]
  coOwnership?: CoOwnership
}

// Payment Types
export interface Payment {
  id: number
  loanId: number
  payerId: number
  amount: number
  principalAmount: number
  interestAmount: number
  penaltyAmount: number
  paymentMethod: 'mpesa' | 'airtel_money' | 'bank_transfer' | 'cash' | 'other'
  transactionId: string
  externalTransactionId?: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded'
  paymentType: 'regular' | 'partial' | 'full' | 'penalty' | 'late_fee'
  reference?: string
  notes?: string
  processedAt?: string
  failureReason?: string
  refundedAt?: string
  refundReason?: string
  createdAt: string
  updatedAt: string
  loan?: Loan
  payer?: User
}

// Co-ownership Types
export interface CoOwnership {
  id: number
  loanId: number
  assetId: number
  borrowerId: number
  lenderId: number
  borrowerEquityPercentage: number
  lenderEquityPercentage: number
  activatedAt: string
  principalPaidAtActivation: number
  assetValueAtActivation: number
  agreementTerms: Record<string, any>
  status: 'active' | 'dissolved' | 'transferred'
  dissolvedAt?: string
  dissolutionReason?: string
  transferredAt?: string
  transferDetails?: Record<string, any>
  createdAt: string
  updatedAt: string
  loan?: Loan
  asset?: Asset
  borrower?: User
  lender?: User
}

// Asset Valuation Types
export interface AssetValuation {
  id: number
  assetId: number
  valuerId?: number
  valuationType: 'automated' | 'professional' | 'market_comparison' | 'insurance'
  currentValue: number
  previousValue?: number
  depreciationRate?: number
  marketConditions?: string
  valuationMethod: string
  notes?: string
  supportingDocuments?: string[]
  isVerified: boolean
  verifiedBy?: number
  verifiedAt?: string
  expiresAt?: string
  createdAt: string
  updatedAt: string
  asset?: Asset
  valuer?: User
  verifier?: User
}

// Asset Sale Types
export interface AssetSale {
  id: number
  assetId: number
  sellerId: number
  loanId?: number
  saleType: 'borrower_initiated' | 'lender_repossession' | 'voluntary'
  listingPrice: number
  reservePrice: number
  currentHighestBid?: number
  finalSalePrice?: number
  buyerId?: number
  status: 'active' | 'sold' | 'cancelled' | 'expired'
  auctionStartDate: string
  auctionEndDate: string
  description: string
  terms: Record<string, any>
  viewingSchedule?: Record<string, any>
  soldAt?: string
  cancelledAt?: string
  cancellationReason?: string
  proceedsDistributed: boolean
  distributionDetails?: Record<string, any>
  createdAt: string
  updatedAt: string
  asset?: Asset
  seller?: User
  buyer?: User
  loan?: Loan
  bids?: SaleBid[]
}

export interface SaleBid {
  id: number
  assetSaleId: number
  bidderId: number
  bidAmount: number
  status: 'active' | 'outbid' | 'winning' | 'withdrawn'
  isAutoBid: boolean
  maxAutoBidAmount?: number
  bidIncrement?: number
  notes?: string
  withdrawnAt?: string
  withdrawalReason?: string
  createdAt: string
  updatedAt: string
  assetSale?: AssetSale
  bidder?: User
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  message?: string
  data?: T
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  meta: {
    total: number
    perPage: number
    currentPage: number
    lastPage: number
    firstPage: number
    firstPageUrl: string
    lastPageUrl: string
    nextPageUrl?: string
    previousPageUrl?: string
  }
}

// Dashboard Types
export interface DashboardStats {
  totalUsers: number
  totalBorrowers: number
  totalLenders: number
  totalLoans: number
  activeLoans: number
  defaultedLoans: number
  totalLoanAmount: number
  totalPayments: number
  totalAssets: number
  coOwnershipActive: number
  pendingKyc: number
  recentActivity: ActivityItem[]
}

export interface ActivityItem {
  id: number
  type: 'loan_created' | 'payment_made' | 'kyc_submitted' | 'asset_added' | 'co_ownership_activated'
  description: string
  userId: number
  user?: User
  createdAt: string
}

// Form Types
export interface LoginForm {
  email: string
  password: string
}

export interface UserFilters {
  userType?: string
  kycStatus?: string
  isActive?: boolean
  search?: string
}

export interface LoanFilters {
  status?: string
  borrowerId?: number
  lenderId?: number
  assetType?: string
  search?: string
}

export interface AssetFilters {
  type?: string
  status?: string
  condition?: string
  search?: string
}

export interface PaymentFilters {
  status?: string
  paymentMethod?: string
  loanId?: number
  search?: string
}
