{"name": "hakimali-admin-panel", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "axios": "^1.6.2", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "react-table": "^7.8.0", "@types/react-table": "^7.7.18", "react-router-dom": "^6.20.1", "@types/react-router-dom": "^5.3.3", "zustand": "^4.4.7", "react-dropzone": "^14.2.3", "react-select": "^5.8.0", "react-datepicker": "^4.25.0", "@types/react-datepicker": "^4.19.4"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9"}}