#!/bin/bash

# HakiMali Fix and Run Script
echo "🔧 Fixing HakiMali dependencies and starting all services..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_status "Checking prerequisites..."

if ! command_exists node; then
    print_error "Node.js is not installed!"
    exit 1
fi

if ! command_exists npm; then
    print_error "npm is not installed!"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ required. Current: $(node -v)"
    exit 1
fi

print_success "Node.js $(node -v) and npm $(npm -v) are available"

# Clean everything thoroughly
print_status "Cleaning npm cache and existing installations..."

# Global npm cache clean
npm cache clean --force

# Remove all node_modules and lock files
rm -rf backend/node_modules backend/package-lock.json
rm -rf admin-panel/node_modules admin-panel/package-lock.json  
rm -rf mpesa-miniapp/node_modules mpesa-miniapp/package-lock.json
rm -rf mobile/node_modules mobile/package-lock.json

print_success "Cleaned all existing installations"

# Fix mobile package.json - remove problematic version and use a working one
print_status "Fixing mobile package.json..."
cd mobile

# Remove the problematic react-native-super-grid dependency temporarily
sed -i '/react-native-super-grid/d' package.json

# Add it back with a working version
sed -i '/"react-native-vector-icons"/i\    "react-native-super-grid": "^4.9.0",' package.json

cd ..

print_success "Fixed mobile package.json"

# Install backend dependencies
print_status "Installing backend dependencies..."
cd backend
if npm install; then
    print_success "Backend dependencies installed successfully"
else
    print_error "Failed to install backend dependencies"
    exit 1
fi
cd ..

# Install admin panel dependencies
print_status "Installing admin panel dependencies..."
cd admin-panel
if npm install; then
    print_success "Admin panel dependencies installed successfully"
else
    print_error "Failed to install admin panel dependencies"
    exit 1
fi
cd ..

# Install M-Pesa mini app dependencies
print_status "Installing M-Pesa mini app dependencies..."
cd mpesa-miniapp
if npm install; then
    print_success "M-Pesa mini app dependencies installed successfully"
else
    print_error "Failed to install M-Pesa mini app dependencies"
    exit 1
fi
cd ..

# Install mobile dependencies with Expo
print_status "Installing mobile app dependencies..."
cd mobile

# Check if Expo CLI is installed globally
if ! command_exists expo; then
    print_status "Installing Expo CLI globally..."
    npm install -g @expo/cli
fi

if npm install; then
    print_success "Mobile app dependencies installed successfully"
else
    print_warning "Mobile app dependencies installation had issues, but continuing..."
fi
cd ..

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p backend/uploads
mkdir -p backend/tmp
mkdir -p logs

# Check database requirements
print_status "Checking database services..."

# Check PostgreSQL
if command_exists psql; then
    print_success "PostgreSQL client available"
else
    print_warning "PostgreSQL not found. Install with: sudo apt install postgresql postgresql-contrib"
fi

# Check Redis
if command_exists redis-cli; then
    if redis-cli ping >/dev/null 2>&1; then
        print_success "Redis is running"
    else
        print_warning "Redis is installed but not running. Start with: sudo systemctl start redis-server"
    fi
else
    print_warning "Redis not found. Install with: sudo apt install redis-server"
fi

# Create PID directory for process tracking
mkdir -p .pids

# Function to start a service and track its PID
start_service() {
    local name=$1
    local dir=$2
    local command=$3
    local port=$4
    
    print_status "Starting $name..."
    
    # Kill any existing process on the port
    if lsof -ti:$port >/dev/null 2>&1; then
        print_warning "Port $port is in use, killing existing process..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # Start the service
    cd $dir
    $command &
    local pid=$!
    echo $pid > "../.pids/${name}.pid"
    cd ..
    
    print_success "$name started with PID $pid on port $port"
    
    # Wait a moment for the service to start
    sleep 3
}

# Start all services
print_status "Starting all HakiMali services..."

# Start backend
start_service "backend" "backend" "npm run dev" "3333"

# Start admin panel
start_service "admin-panel" "admin-panel" "npm run dev" "3000"

# Start M-Pesa mini app
start_service "mpesa-miniapp" "mpesa-miniapp" "npm run dev" "3001"

# Start mobile app with Expo
print_status "Starting mobile app with Expo..."
cd mobile
expo start --web &
EXPO_PID=$!
echo $EXPO_PID > "../.pids/mobile.pid"
cd ..
print_success "Mobile app (Expo) started with PID $EXPO_PID"

# Wait for services to fully start
print_status "Waiting for services to fully start..."
sleep 10

# Check service health
print_status "Checking service health..."

check_service() {
    local name=$1
    local url=$2
    
    if curl -f -s $url >/dev/null 2>&1; then
        print_success "$name is healthy at $url"
        return 0
    else
        print_warning "$name is not responding at $url (may still be starting up)"
        return 1
    fi
}

# Check each service
check_service "Backend API" "http://localhost:3333"
check_service "Admin Panel" "http://localhost:3000"
check_service "M-Pesa Mini App" "http://localhost:3001"

# Mobile app check (Expo web)
if curl -f -s http://localhost:19006 >/dev/null 2>&1; then
    print_success "Mobile App (Expo Web) is healthy at http://localhost:19006"
else
    print_warning "Mobile App (Expo Web) is not responding (may still be starting up)"
fi

# Create stop script
cat > stop-all.sh << 'EOF'
#!/bin/bash

echo "🛑 Stopping all HakiMali services..."

# Function to stop service by PID
stop_service() {
    local name=$1
    local pidfile=".pids/${name}.pid"
    
    if [ -f "$pidfile" ]; then
        local pid=$(cat "$pidfile")
        if kill -0 $pid 2>/dev/null; then
            echo "Stopping $name (PID: $pid)..."
            kill $pid
            sleep 2
            # Force kill if still running
            if kill -0 $pid 2>/dev/null; then
                kill -9 $pid 2>/dev/null
            fi
        fi
        rm "$pidfile"
    fi
}

# Stop all services
stop_service "backend"
stop_service "admin-panel" 
stop_service "mpesa-miniapp"
stop_service "mobile"

# Clean up any remaining processes on our ports
echo "Cleaning up remaining processes..."
lsof -ti:3333 | xargs kill -9 2>/dev/null || true
lsof -ti:3000 | xargs kill -9 2>/dev/null || true
lsof -ti:3001 | xargs kill -9 2>/dev/null || true
lsof -ti:19000,19001,19002,19006 | xargs kill -9 2>/dev/null || true

# Remove PID directory
rm -rf .pids

echo "✅ All services stopped"
EOF

chmod +x stop-all.sh

print_success "🎉 All HakiMali services are starting up!"
echo ""
echo "📱 Access your applications:"
echo "   🔗 Backend API: http://localhost:3333"
echo "   🔗 Admin Panel: http://localhost:3000"
echo "   🔗 M-Pesa Mini App: http://localhost:3001"
echo "   🔗 Mobile App (Expo): http://localhost:19006"
echo "   📱 Expo DevTools: http://localhost:19002"
echo ""
echo "📊 Monitor services:"
echo "   View all processes: ps aux | grep node"
echo "   Check specific ports: lsof -i :3333"
echo ""
echo "🛑 Stop all services:"
echo "   ./stop-all.sh"
echo ""
echo "📝 Development tips:"
echo "   - Services support hot reload"
echo "   - Check terminal output for any errors"
echo "   - Mobile app: Use Expo Go app on your phone or web browser"
echo "   - Backend logs: cd backend && npm run dev"
echo ""

# Keep the script running and show status
print_status "Services are running. Press Ctrl+C to stop monitoring (services will continue running)"
print_status "Use './stop-all.sh' to stop all services"

# Monitor services (optional)
while true; do
    sleep 30
    echo -e "\n${BLUE}[$(date)]${NC} Service Status Check:"
    
    # Check if PIDs are still running
    for service in backend admin-panel mpesa-miniapp mobile; do
        pidfile=".pids/${service}.pid"
        if [ -f "$pidfile" ]; then
            pid=$(cat "$pidfile")
            if kill -0 $pid 2>/dev/null; then
                echo -e "  ✅ $service (PID: $pid) - Running"
            else
                echo -e "  ❌ $service (PID: $pid) - Stopped"
            fi
        else
            echo -e "  ❓ $service - No PID file"
        fi
    done
done
EOF
